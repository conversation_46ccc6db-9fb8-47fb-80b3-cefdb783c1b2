// Dashboard functionality
class Dashboard {
    constructor() {
        this.charts = {};
        this.stats = {
            totalEmployees: 0,
            activeEmployees: 0,
            totalDepartments: 0,
            totalSalaries: 0
        };
    }

    async init() {
        await this.loadStatistics();
        await this.loadCharts();
        await this.loadRecentEmployees();
        await this.loadDepartmentSummary();
        this.setupEventListeners();
    }

    async loadStatistics() {
        try {
            // Load employees
            const employees = await dbManager.getAll('employees');
            this.stats.totalEmployees = employees.length;
            this.stats.activeEmployees = employees.filter(emp => emp.status === 'active').length;

            // Load departments
            const departments = await dbManager.getAll('departments');
            this.stats.totalDepartments = departments.length;

            // Calculate total salaries
            this.stats.totalSalaries = employees
                .filter(emp => emp.status === 'active')
                .reduce((total, emp) => total + (emp.salary || 0), 0);

            // Update UI
            this.updateStatisticsUI();
        } catch (error) {
            console.error('Error loading statistics:', error);
        }
    }

    updateStatisticsUI() {
        document.getElementById('totalEmployees').textContent = this.stats.totalEmployees;
        document.getElementById('activeEmployees').textContent = this.stats.activeEmployees;
        document.getElementById('totalDepartments').textContent = this.stats.totalDepartments;
        document.getElementById('totalSalaries').textContent = this.formatCurrency(this.stats.totalSalaries);
    }

    async loadCharts() {
        await this.loadDepartmentChart();
        await this.loadStatusChart();
    }

    async loadDepartmentChart() {
        try {
            const employees = await dbManager.getAll('employees');
            const departments = await dbManager.getAll('departments');
            
            // Count employees by department
            const departmentCounts = {};
            departments.forEach(dept => {
                departmentCounts[dept.name] = 0;
            });

            employees.forEach(emp => {
                if (departmentCounts.hasOwnProperty(emp.department)) {
                    departmentCounts[emp.department]++;
                }
            });

            const ctx = document.getElementById('departmentChart').getContext('2d');
            this.charts.departmentChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: Object.keys(departmentCounts),
                    datasets: [{
                        label: 'عدد الموظفين',
                        data: Object.values(departmentCounts),
                        backgroundColor: [
                            '#3498db',
                            '#2ecc71',
                            '#f39c12',
                            '#e74c3c',
                            '#9b59b6',
                            '#1abc9c'
                        ],
                        borderColor: [
                            '#2980b9',
                            '#27ae60',
                            '#e67e22',
                            '#c0392b',
                            '#8e44ad',
                            '#16a085'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                stepSize: 1
                            }
                        }
                    },
                    layout: {
                        padding: 10
                    }
                }
            });
        } catch (error) {
            console.error('Error loading department chart:', error);
        }
    }

    async loadStatusChart() {
        try {
            const employees = await dbManager.getAll('employees');
            
            const statusCounts = {
                'نشط': 0,
                'غير نشط': 0,
                'معلق': 0
            };

            employees.forEach(emp => {
                switch (emp.status) {
                    case 'active':
                        statusCounts['نشط']++;
                        break;
                    case 'inactive':
                        statusCounts['غير نشط']++;
                        break;
                    case 'suspended':
                        statusCounts['معلق']++;
                        break;
                }
            });

            const ctx = document.getElementById('statusChart').getContext('2d');
            this.charts.statusChart = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: Object.keys(statusCounts),
                    datasets: [{
                        data: Object.values(statusCounts),
                        backgroundColor: [
                            '#2ecc71',
                            '#e74c3c',
                            '#f39c12'
                        ],
                        borderColor: [
                            '#27ae60',
                            '#c0392b',
                            '#e67e22'
                        ],
                        borderWidth: 2
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    },
                    layout: {
                        padding: 10
                    }
                }
            });
        } catch (error) {
            console.error('Error loading status chart:', error);
        }

        // Enforce height limits after charts are created
        setTimeout(() => {
            this.enforceChartHeightLimits();
        }, 200);
    }

    async loadRecentEmployees() {
        try {
            const employees = await dbManager.getAll('employees');
            
            // Sort by hire date (most recent first) and take first 5
            const recentEmployees = employees
                .sort((a, b) => new Date(b.hireDate) - new Date(a.hireDate))
                .slice(0, 5);

            const tableBody = document.getElementById('recentEmployeesTable');
            
            if (recentEmployees.length === 0) {
                tableBody.innerHTML = '<tr><td colspan="5" class="text-center text-muted">لا توجد بيانات</td></tr>';
                return;
            }

            tableBody.innerHTML = recentEmployees.map(emp => `
                <tr>
                    <td>
                        <div class="d-flex align-items-center">
                            <div class="user-avatar me-2" style="width: 30px; height: 30px; font-size: 0.8rem;">
                                ${emp.name.charAt(0)}
                            </div>
                            ${emp.name}
                        </div>
                    </td>
                    <td>${emp.department}</td>
                    <td>${emp.position}</td>
                    <td>${this.formatDate(emp.hireDate)}</td>
                    <td>
                        <span class="status-badge ${this.getStatusClass(emp.status)}">
                            ${this.getStatusText(emp.status)}
                        </span>
                    </td>
                </tr>
            `).join('');
        } catch (error) {
            console.error('Error loading recent employees:', error);
        }
    }

    async loadDepartmentSummary() {
        try {
            const employees = await dbManager.getAll('employees');
            const departments = await dbManager.getAll('departments');
            
            const departmentSummary = departments.map(dept => {
                const deptEmployees = employees.filter(emp => emp.department === dept.name);
                const activeEmployees = deptEmployees.filter(emp => emp.status === 'active');
                const totalSalaries = activeEmployees.reduce((sum, emp) => sum + (emp.salary || 0), 0);
                const avgSalary = activeEmployees.length > 0 ? totalSalaries / activeEmployees.length : 0;

                return {
                    name: dept.name,
                    totalEmployees: deptEmployees.length,
                    activeEmployees: activeEmployees.length,
                    avgSalary: avgSalary,
                    totalSalaries: totalSalaries
                };
            });

            const tableBody = document.getElementById('departmentSummaryTable');
            
            if (departmentSummary.length === 0) {
                tableBody.innerHTML = '<tr><td colspan="5" class="text-center text-muted">لا توجد بيانات</td></tr>';
                return;
            }

            tableBody.innerHTML = departmentSummary.map(dept => `
                <tr>
                    <td><strong>${dept.name}</strong></td>
                    <td>${dept.totalEmployees}</td>
                    <td>${dept.activeEmployees}</td>
                    <td>${this.formatCurrency(dept.avgSalary)}</td>
                    <td>${this.formatCurrency(dept.totalSalaries)}</td>
                </tr>
            `).join('');
        } catch (error) {
            console.error('Error loading department summary:', error);
        }
    }

    setupEventListeners() {
        // Sidebar toggle
        const sidebarToggle = document.getElementById('sidebarToggle');
        const sidebar = document.getElementById('sidebar');
        const mainContent = document.getElementById('mainContent');

        if (sidebarToggle) {
            sidebarToggle.addEventListener('click', () => {
                sidebar.classList.toggle('collapsed');
                mainContent.classList.toggle('expanded');
            });
        }

        // Auto refresh every 5 minutes
        setInterval(() => {
            this.refreshData();
        }, 5 * 60 * 1000);
    }

    async refreshData() {
        await this.loadStatistics();
        await this.loadRecentEmployees();
        await this.loadDepartmentSummary();
        
        // Update charts
        if (this.charts.departmentChart) {
            this.charts.departmentChart.destroy();
        }
        if (this.charts.statusChart) {
            this.charts.statusChart.destroy();
        }
        await this.loadCharts();

        // Enforce height limits after refresh
        setTimeout(() => {
            this.enforceChartHeightLimits();
        }, 200);
    }

    // Utility functions
    formatCurrency(amount) {
        return new Intl.NumberFormat('ar-SA', {
            style: 'currency',
            currency: 'omr',
            minimumFractionDigits: 0
        }).format(amount);
    }

    formatDate(dateString) {
        if (!dateString) return '-';
        const date = new Date(dateString);
        return date.toLocaleDateString('ar-SA');
    }

    getStatusClass(status) {
        switch (status) {
            case 'active': return 'status-active';
            case 'inactive': return 'status-inactive';
            case 'suspended': return 'status-suspended';
            default: return 'status-inactive';
        }
    }

    getStatusText(status) {
        switch (status) {
            case 'active': return 'نشط';
            case 'inactive': return 'غير نشط';
            case 'suspended': return 'معلق';
            default: return 'غير محدد';
        }
    }

    enforceChartHeightLimits() {
        // Force all canvas elements to respect height limits
        const canvases = document.querySelectorAll('canvas');
        canvases.forEach(canvas => {
            canvas.style.maxHeight = '350px';
            canvas.style.height = '350px';

            // Also set the parent container height
            const parent = canvas.closest('.stat-card');
            if (parent) {
                parent.style.maxHeight = '400px';
                parent.style.overflow = 'hidden';
            }
        });
    }
}

// Initialize dashboard
const dashboard = new Dashboard();

document.addEventListener('DOMContentLoaded', async function() {
    // Initialize database first
    await initializeDatabase();
    
    // Initialize dashboard
    await dashboard.init();
});
