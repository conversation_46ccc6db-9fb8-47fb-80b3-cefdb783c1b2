<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>حساب الرواتب - نظام إدارة الموظفين</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="styles.css" rel="stylesheet">
</head>
<body>
    <!-- Sidebar -->
    <nav class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <h4 class="text-white mb-0">
                <i class="fas fa-users-cog me-2"></i>
                <span class="sidebar-text">نظام الموظفين</span>
            </h4>
        </div>
        <ul class="sidebar-menu">
            <li>
                <a href="dashboard.html">
                    <i class="fas fa-tachometer-alt"></i>
                    <span class="sidebar-text">لوحة التحكم</span>
                </a>
            </li>
            <li>
                <a href="employees.html">
                    <i class="fas fa-users"></i>
                    <span class="sidebar-text">إدارة الموظفين</span>
                </a>
            </li>
            <li>
                <a href="payroll.html" class="active">
                    <i class="fas fa-calculator"></i>
                    <span class="sidebar-text">حساب الرواتب</span>
                </a>
            </li>
            <li>
                <a href="reports.html">
                    <i class="fas fa-chart-bar"></i>
                    <span class="sidebar-text">التقارير</span>
                </a>
            </li>
            <li>
                <a href="attendance.html">
                    <i class="fas fa-clock"></i>
                    <span class="sidebar-text">الحضور والانصراف</span>
                </a>
            </li>
            <li>
                <a href="settings.html">
                    <i class="fas fa-cog"></i>
                    <span class="sidebar-text">الإعدادات</span>
                </a>
            </li>
            <li>
                <a href="#" class="logout-btn">
                    <i class="fas fa-sign-out-alt"></i>
                    <span class="sidebar-text">تسجيل الخروج</span>
                </a>
            </li>
        </ul>
    </nav>

    <!-- Main Content -->
    <div class="main-content" id="mainContent">
        <!-- Header -->
        <header class="header">
            <div class="d-flex align-items-center">
                <button class="btn btn-link text-dark me-3" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h3 class="header-title">حساب الرواتب</h3>
            </div>
            <div class="user-info">
                <span class="user-name me-2"></span>
                <span class="user-role badge bg-primary me-2"></span>
                <div class="user-avatar"></div>
            </div>
        </header>

        <!-- Content Area -->
        <div class="content-area">
            <!-- Payroll Period Selection -->
            <div class="form-container mb-4">
                <h5 class="mb-3">فترة الراتب</h5>
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label for="payrollMonth" class="form-label">الشهر</label>
                        <select class="form-select" id="payrollMonth">
                            <option value="1">يناير</option>
                            <option value="2">فبراير</option>
                            <option value="3">مارس</option>
                            <option value="4">أبريل</option>
                            <option value="5">مايو</option>
                            <option value="6">يونيو</option>
                            <option value="7">يوليو</option>
                            <option value="8">أغسطس</option>
                            <option value="9">سبتمبر</option>
                            <option value="10">أكتوبر</option>
                            <option value="11">نوفمبر</option>
                            <option value="12">ديسمبر</option>
                        </select>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label for="payrollYear" class="form-label">السنة</label>
                        <select class="form-select" id="payrollYear">
                        </select>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label for="departmentFilter" class="form-label">القسم</label>
                        <select class="form-select" id="departmentFilter">
                            <option value="">جميع الأقسام</option>
                        </select>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button class="btn btn-primary" id="calculatePayrollBtn">
                                <i class="fas fa-calculator me-1"></i>
                                حساب الرواتب
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Payroll Summary -->
            <div class="row mb-4" id="payrollSummary" style="display: none;">
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="stat-card info">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="stat-number" id="totalEmployeesCount">0</h3>
                                <p class="stat-label">عدد الموظفين</p>
                            </div>
                            <i class="fas fa-users fa-2x text-info"></i>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="stat-card success">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="stat-number" id="totalBasicSalary">0</h3>
                                <p class="stat-label">إجمالي الراتب الأساسي</p>
                            </div>
                            <i class="fas fa-money-bill fa-2x text-success"></i>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="stat-card warning">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="stat-number" id="totalDeductions">0</h3>
                                <p class="stat-label">إجمالي الخصومات</p>
                            </div>
                            <i class="fas fa-minus-circle fa-2x text-warning"></i>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="stat-card danger">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="stat-number" id="totalNetSalary">0</h3>
                                <p class="stat-label">صافي الراتب</p>
                            </div>
                            <i class="fas fa-hand-holding-usd fa-2x text-danger"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Payroll Table -->
            <div class="table-container" id="payrollTable" style="display: none;">
                <div class="table-header">
                    <h5 class="mb-0">كشف الرواتب</h5>
                    <div>
                        <button class="btn btn-success me-2" id="savePayrollBtn">
                            <i class="fas fa-save me-1"></i>
                            حفظ الكشف
                        </button>
                        <button class="btn btn-info me-2" id="exportPayrollBtn">
                            <i class="fas fa-file-pdf me-1"></i>
                            تصدير PDF
                        </button>
                        <button class="btn btn-primary" id="printPayrollBtn">
                            <i class="fas fa-print me-1"></i>
                            طباعة
                        </button>
                    </div>
                </div>
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>الرقم الوظيفي</th>
                                <th>اسم الموظف</th>
                                <th>القسم</th>
                                <th>الراتب الأساسي</th>
                                <th>أيام العمل</th>
                                <th>الساعات الإضافية</th>
                                <th>البدلات</th>
                                <th>الخصومات</th>
                                <th>صافي الراتب</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="payrollTableBody">
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Payroll Details Modal -->
    <div class="modal fade" id="payrollDetailsModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تفاصيل راتب الموظف</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="payrollDetailsForm">
                        <input type="hidden" id="employeeIdForPayroll">
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <h6 id="employeeNameForPayroll"></h6>
                                <p class="text-muted" id="employeeDepartmentForPayroll"></p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>الراتب الأساسي:</strong> <span id="basicSalaryDisplay"></span></p>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-success">الإضافات</h6>
                                <div class="mb-3">
                                    <label for="workingDays" class="form-label">أيام العمل</label>
                                    <input type="number" class="form-control" id="workingDays" min="0" max="31" value="22">
                                </div>
                                <div class="mb-3">
                                    <label for="overtimeHours" class="form-label">الساعات الإضافية</label>
                                    <input type="number" class="form-control" id="overtimeHours" min="0" step="0.5" value="0">
                                </div>
                                <div class="mb-3">
                                    <label for="allowances" class="form-label">البدلات</label>
                                    <input type="number" class="form-control" id="allowances" min="0" step="0.01" value="0">
                                </div>
                                <div class="mb-3">
                                    <label for="bonus" class="form-label">المكافآت</label>
                                    <input type="number" class="form-control" id="bonus" min="0" step="0.01" value="0">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-danger">الخصومات</h6>
                                <div class="mb-3">
                                    <label for="absenceDays" class="form-label">أيام الغياب</label>
                                    <input type="number" class="form-control" id="absenceDays" min="0" max="31" value="0">
                                </div>
                                <div class="mb-3">
                                    <label for="latePenalty" class="form-label">خصم التأخير</label>
                                    <input type="number" class="form-control" id="latePenalty" min="0" step="0.01" value="0">
                                </div>
                                <div class="mb-3">
                                    <label for="otherDeductions" class="form-label">خصومات أخرى</label>
                                    <input type="number" class="form-control" id="otherDeductions" min="0" step="0.01" value="0">
                                </div>
                                <div class="mb-3">
                                    <label for="insurance" class="form-label">التأمين الاجتماعي</label>
                                    <input type="number" class="form-control" id="insurance" min="0" step="0.01" value="0">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="alert alert-info">
                                    <h6>ملخص الحساب:</h6>
                                    <div class="row">
                                        <div class="col-md-4">
                                            <p><strong>إجمالي الإضافات:</strong> <span id="totalAdditions">0</span> ريال</p>
                                        </div>
                                        <div class="col-md-4">
                                            <p><strong>إجمالي الخصومات:</strong> <span id="totalDeductionsCalc">0</span> ريال</p>
                                        </div>
                                        <div class="col-md-4">
                                            <p><strong>صافي الراتب:</strong> <span id="netSalaryCalc">0</span> ريال</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" id="savePayrollDetailsBtn">
                        <i class="fas fa-save me-1"></i>
                        حفظ التفاصيل
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="database.js"></script>
    <script src="auth.js"></script>
    <script src="payroll.js"></script>
</body>
</html>
