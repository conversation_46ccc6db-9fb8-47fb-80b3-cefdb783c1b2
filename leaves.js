// Leave Management System
class LeavesManager {
    constructor() {
        this.leaves = [];
        this.employees = [];
        this.leaveBalances = [];
        this.currentEditId = null;
        this.leaveTypes = {
            annual: 'إجازة سنوية',
            sick: 'إجازة مرضية',
            emergency: 'إجازة طارئة',
            maternity: 'إجازة أمومة',
            paternity: 'إجازة أبوة',
            unpaid: 'إجازة بدون راتب',
            study: 'إجازة دراسية',
            hajj: 'إجازة حج'
        };
        this.statusTypes = {
            pending: 'في الانتظار',
            approved: 'معتمدة',
            rejected: 'مرفوضة',
            cancelled: 'ملغية'
        };
    }

    async init() {
        await this.loadData();
        this.setupEventListeners();
        this.renderAllTables();
        this.updateSummaryCards();
        this.initializeFilters();
    }

    async loadData() {
        try {
            // Load employees first
            await this.loadEmployees();

            // Load leaves
            const leavesData = localStorage.getItem('leaves');
            this.leaves = leavesData ? JSON.parse(leavesData) : [];

            // Load leave balances
            const balancesData = localStorage.getItem('leaveBalances');
            this.leaveBalances = balancesData ? JSON.parse(balancesData) : [];

            // Initialize balances for new employees
            this.initializeEmployeeBalances();

            // Populate employee dropdowns
            this.populateEmployeeDropdowns();

        } catch (error) {
            console.error('Error loading leave data:', error);
            this.showAlert('حدث خطأ في تحميل بيانات الإجازات', 'danger');
        }
    }

    async loadEmployees() {
        try {
            // Try to load from localStorage first
            const employeesData = localStorage.getItem('employees');
            if (employeesData) {
                this.employees = JSON.parse(employeesData);
                console.log('Loaded employees from localStorage:', this.employees.length);
            }

            // If no employees found, create sample data
            if (this.employees.length === 0) {
                console.log('No employees found, creating sample data...');
                this.createSampleEmployees();
            }

        } catch (error) {
            console.error('Error loading employees:', error);
            this.createSampleEmployees();
        }
    }

    createSampleEmployees() {
        this.employees = [
            {
                id: '1',
                name: 'أحمد محمد علي',
                employeeNumber: 'EMP001',
                department: 'تقنية المعلومات',
                position: 'مطور برمجيات',
                salary: 8000,
                email: '<EMAIL>',
                phone: '0501234567',
                hireDate: '2023-01-15',
                status: 'active'
            },
            {
                id: '2',
                name: 'فاطمة أحمد السالم',
                employeeNumber: 'EMP002',
                department: 'الموارد البشرية',
                position: 'أخصائي موارد بشرية',
                salary: 6500,
                email: '<EMAIL>',
                phone: '0507654321',
                hireDate: '2023-02-01',
                status: 'active'
            },
            {
                id: '3',
                name: 'محمد عبدالله الأحمد',
                employeeNumber: 'EMP003',
                department: 'المالية',
                position: 'محاسب',
                salary: 7000,
                email: '<EMAIL>',
                phone: '0509876543',
                hireDate: '2023-03-10',
                status: 'active'
            },
            {
                id: '4',
                name: 'نورا سعد المطيري',
                employeeNumber: 'EMP004',
                department: 'التسويق',
                position: 'أخصائي تسويق',
                salary: 5500,
                email: '<EMAIL>',
                phone: '0502468135',
                hireDate: '2023-04-05',
                status: 'active'
            },
            {
                id: '5',
                name: 'خالد عبدالرحمن القحطاني',
                employeeNumber: 'EMP005',
                department: 'المبيعات',
                position: 'مندوب مبيعات',
                salary: 6000,
                email: '<EMAIL>',
                phone: '0503691472',
                hireDate: '2023-05-20',
                status: 'active'
            }
        ];

        // Save to localStorage
        localStorage.setItem('employees', JSON.stringify(this.employees));
        console.log('Created and saved sample employees:', this.employees.length);
    }

    initializeEmployeeBalances() {
        this.employees.forEach(employee => {
            const existingBalance = this.leaveBalances.find(b => b.employeeId === employee.id);
            if (!existingBalance) {
                this.leaveBalances.push({
                    employeeId: employee.id,
                    annualLeave: {
                        total: 30, // 30 days annual leave
                        used: 0,
                        remaining: 30
                    },
                    sickLeave: {
                        total: 15, // 15 days sick leave
                        used: 0,
                        remaining: 15
                    },
                    lastUpdated: new Date().toISOString()
                });
            }
        });
        this.saveToStorage('leaveBalances', this.leaveBalances);
    }

    populateEmployeeDropdowns() {
        const dropdown = document.getElementById('leaveEmployee');
        if (dropdown) {
            dropdown.innerHTML = '<option value="">اختر الموظف</option>';

            console.log('Populating dropdown with employees:', this.employees.length);

            if (this.employees && this.employees.length > 0) {
                this.employees.forEach(employee => {
                    const option = document.createElement('option');
                    option.value = employee.id;
                    option.textContent = `${employee.name} - ${employee.employeeNumber || employee.department}`;
                    dropdown.appendChild(option);
                });
                console.log('Dropdown populated successfully');
            } else {
                console.log('No employees found to populate dropdown');
                const option = document.createElement('option');
                option.value = '';
                option.textContent = 'لا توجد موظفين - يرجى إضافة موظفين أولاً';
                option.disabled = true;
                dropdown.appendChild(option);
            }
        } else {
            console.log('Employee dropdown not found');
        }
    }

    setupEventListeners() {
        // Save buttons
        document.getElementById('saveLeaveBtn').addEventListener('click', () => this.saveLeave());
        document.getElementById('saveBalanceBtn').addEventListener('click', () => this.saveBalance());
        document.getElementById('confirmDeleteLeaveBtn').addEventListener('click', () => this.confirmDelete());

        // Date calculations
        document.getElementById('leaveStartDate').addEventListener('change', () => this.calculateLeaveDays());
        document.getElementById('leaveEndDate').addEventListener('change', () => this.calculateLeaveDays());

        // Filters
        document.getElementById('filterStatus').addEventListener('change', () => this.applyFilters());
        document.getElementById('filterType').addEventListener('change', () => this.applyFilters());

        // Modal events to refresh employee dropdowns
        const leaveModal = document.getElementById('leaveModal');
        if (leaveModal) {
            leaveModal.addEventListener('show.bs.modal', () => {
                this.populateEmployeeDropdowns();
            });
        }

        // Set current month and year for calendar
        const currentDate = new Date();
        const calendarMonth = document.getElementById('calendarMonth');
        const calendarYear = document.getElementById('calendarYear');

        if (calendarMonth) {
            calendarMonth.value = currentDate.getMonth() + 1;
        }

        if (calendarYear) {
            calendarYear.value = currentDate.getFullYear();
        }
    }

    calculateLeaveDays() {
        const startDate = document.getElementById('leaveStartDate').value;
        const endDate = document.getElementById('leaveEndDate').value;

        if (startDate && endDate) {
            const start = new Date(startDate);
            const end = new Date(endDate);
            
            if (end >= start) {
                const timeDiff = end.getTime() - start.getTime();
                const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24)) + 1; // Include both start and end dates
                document.getElementById('leaveDays').value = daysDiff;
            } else {
                document.getElementById('leaveDays').value = 0;
                this.showAlert('تاريخ النهاية يجب أن يكون بعد تاريخ البداية', 'warning');
            }
        }
    }

    saveLeave() {
        const form = document.getElementById('leaveForm');
        if (!form.checkValidity()) {
            form.reportValidity();
            return;
        }

        const leaveData = {
            id: this.currentEditId || Date.now().toString(),
            employeeId: document.getElementById('leaveEmployee').value,
            type: document.getElementById('leaveType').value,
            startDate: document.getElementById('leaveStartDate').value,
            endDate: document.getElementById('leaveEndDate').value,
            days: parseInt(document.getElementById('leaveDays').value),
            reason: document.getElementById('leaveReason').value,
            notes: document.getElementById('leaveNotes').value,
            status: document.getElementById('leaveStatus').value,
            withPay: document.getElementById('leaveWithPay').checked,
            requestDate: this.currentEditId ? this.leaves.find(l => l.id === this.currentEditId)?.requestDate : new Date().toISOString(),
            approvedBy: null,
            approvedDate: null
        };

        // Check leave balance
        if (!this.checkLeaveBalance(leaveData)) {
            return;
        }

        if (this.currentEditId) {
            // Update existing leave
            const index = this.leaves.findIndex(l => l.id === this.currentEditId);
            if (index !== -1) {
                const oldLeave = this.leaves[index];
                this.leaves[index] = leaveData;
                
                // Update balance if status changed
                this.updateLeaveBalance(oldLeave, leaveData);
            }
        } else {
            // Add new leave
            this.leaves.push(leaveData);
            
            // Update balance for approved leaves
            if (leaveData.status === 'approved') {
                this.updateLeaveBalance(null, leaveData);
            }
        }

        this.saveToStorage('leaves', this.leaves);
        this.renderAllTables();
        this.updateSummaryCards();

        // Close modal and reset form
        const modal = bootstrap.Modal.getInstance(document.getElementById('leaveModal'));
        modal.hide();
        this.resetLeaveForm();

        this.showAlert(this.currentEditId ? 'تم تحديث طلب الإجازة بنجاح' : 'تم إضافة طلب الإجازة بنجاح', 'success');
        this.currentEditId = null;
    }

    checkLeaveBalance(leaveData) {
        if (leaveData.status !== 'approved') {
            return true; // Only check balance for approved leaves
        }

        const balance = this.leaveBalances.find(b => b.employeeId === leaveData.employeeId);
        if (!balance) {
            this.showAlert('لم يتم العثور على رصيد الإجازات للموظف', 'danger');
            return false;
        }

        let availableBalance = 0;
        if (leaveData.type === 'annual') {
            availableBalance = balance.annualLeave.remaining;
        } else if (leaveData.type === 'sick') {
            availableBalance = balance.sickLeave.remaining;
        } else {
            return true; // Other leave types don't affect balance
        }

        if (leaveData.days > availableBalance) {
            this.showAlert(`رصيد الإجازة غير كافي. المتاح: ${availableBalance} يوم`, 'danger');
            return false;
        }

        return true;
    }

    updateLeaveBalance(oldLeave, newLeave) {
        const balance = this.leaveBalances.find(b => b.employeeId === newLeave.employeeId);
        if (!balance) return;

        // Restore old leave balance if exists
        if (oldLeave && oldLeave.status === 'approved') {
            if (oldLeave.type === 'annual') {
                balance.annualLeave.used -= oldLeave.days;
                balance.annualLeave.remaining += oldLeave.days;
            } else if (oldLeave.type === 'sick') {
                balance.sickLeave.used -= oldLeave.days;
                balance.sickLeave.remaining += oldLeave.days;
            }
        }

        // Apply new leave balance
        if (newLeave.status === 'approved') {
            if (newLeave.type === 'annual') {
                balance.annualLeave.used += newLeave.days;
                balance.annualLeave.remaining -= newLeave.days;
            } else if (newLeave.type === 'sick') {
                balance.sickLeave.used += newLeave.days;
                balance.sickLeave.remaining -= newLeave.days;
            }
        }

        balance.lastUpdated = new Date().toISOString();
        this.saveToStorage('leaveBalances', this.leaveBalances);
    }

    saveBalance() {
        const employeeId = document.getElementById('balanceEmployeeId').value;
        const annualBalance = parseInt(document.getElementById('annualLeaveBalance').value);
        const sickBalance = parseInt(document.getElementById('sickLeaveBalance').value);

        const balance = this.leaveBalances.find(b => b.employeeId === employeeId);
        if (balance) {
            balance.annualLeave.total = annualBalance;
            balance.annualLeave.remaining = annualBalance - balance.annualLeave.used;
            balance.sickLeave.total = sickBalance;
            balance.sickLeave.remaining = sickBalance - balance.sickLeave.used;
            balance.lastUpdated = new Date().toISOString();

            this.saveToStorage('leaveBalances', this.leaveBalances);
            this.renderLeaveBalanceTable();

            const modal = bootstrap.Modal.getInstance(document.getElementById('balanceModal'));
            modal.hide();

            this.showAlert('تم تحديث رصيد الإجازات بنجاح', 'success');
        }
    }

    saveToStorage(key, data) {
        try {
            localStorage.setItem(key, JSON.stringify(data));
        } catch (error) {
            console.error(`Error saving ${key}:`, error);
            this.showAlert(`حدث خطأ في حفظ ${key}`, 'danger');
        }
    }

    getEmployeeName(employeeId) {
        const employee = this.employees.find(emp => emp.id === employeeId);
        return employee ? employee.name : 'غير محدد';
    }

    getEmployeeDepartment(employeeId) {
        const employee = this.employees.find(emp => emp.id === employeeId);
        return employee ? employee.department : 'غير محدد';
    }

    formatDate(dateString) {
        return new Date(dateString).toLocaleDateString('ar-SA');
    }

    getStatusBadge(status) {
        const statusConfig = {
            pending: 'warning',
            approved: 'success',
            rejected: 'danger',
            cancelled: 'secondary'
        };

        const badgeClass = statusConfig[status] || 'secondary';
        const text = this.statusTypes[status] || status;

        return `<span class="badge bg-${badgeClass}">${text}</span>`;
    }

    showAlert(message, type = 'info') {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        alertDiv.style.cssText = 'top: 20px; left: 20px; z-index: 9999; min-width: 300px;';
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(alertDiv);

        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 5000);
    }

    // Render Tables
    renderAllTables() {
        this.renderAllLeavesTable();
        this.renderPendingLeavesTable();
        this.renderLeaveBalanceTable();
    }

    renderAllLeavesTable() {
        const tbody = document.getElementById('allLeavesTable');
        if (!tbody) return;

        let filteredLeaves = this.leaves;

        // Apply filters
        const statusFilter = document.getElementById('filterStatus')?.value;
        const typeFilter = document.getElementById('filterType')?.value;

        if (statusFilter) {
            filteredLeaves = filteredLeaves.filter(leave => leave.status === statusFilter);
        }

        if (typeFilter) {
            filteredLeaves = filteredLeaves.filter(leave => leave.type === typeFilter);
        }

        if (filteredLeaves.length === 0) {
            tbody.innerHTML = '<tr><td colspan="9" class="text-center text-muted">لا توجد طلبات إجازات</td></tr>';
            return;
        }

        tbody.innerHTML = filteredLeaves.map(leave => `
            <tr>
                <td>${this.getEmployeeName(leave.employeeId)}</td>
                <td>${this.leaveTypes[leave.type]}</td>
                <td>${this.formatDate(leave.startDate)}</td>
                <td>${this.formatDate(leave.endDate)}</td>
                <td><span class="badge bg-info">${leave.days} يوم</span></td>
                <td title="${leave.reason}">${leave.reason.length > 30 ? leave.reason.substring(0, 30) + '...' : leave.reason}</td>
                <td>${this.getStatusBadge(leave.status)}</td>
                <td>${this.formatDate(leave.requestDate)}</td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="leavesManager.editLeave('${leave.id}')" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        ${leave.status === 'pending' ? `
                            <button class="btn btn-outline-success" onclick="leavesManager.approveLeave('${leave.id}')" title="اعتماد">
                                <i class="fas fa-check"></i>
                            </button>
                            <button class="btn btn-outline-danger" onclick="leavesManager.rejectLeave('${leave.id}')" title="رفض">
                                <i class="fas fa-times"></i>
                            </button>
                        ` : ''}
                        <button class="btn btn-outline-danger" onclick="leavesManager.deleteLeave('${leave.id}')" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }

    renderPendingLeavesTable() {
        const tbody = document.getElementById('pendingLeavesTable');
        if (!tbody) return;

        const pendingLeaves = this.leaves.filter(leave => leave.status === 'pending');

        if (pendingLeaves.length === 0) {
            tbody.innerHTML = '<tr><td colspan="7" class="text-center text-muted">لا توجد طلبات في الانتظار</td></tr>';
            return;
        }

        tbody.innerHTML = pendingLeaves.map(leave => `
            <tr>
                <td>${this.getEmployeeName(leave.employeeId)}</td>
                <td>${this.leaveTypes[leave.type]}</td>
                <td>${this.formatDate(leave.startDate)} - ${this.formatDate(leave.endDate)}</td>
                <td><span class="badge bg-info">${leave.days} يوم</span></td>
                <td title="${leave.reason}">${leave.reason.length > 40 ? leave.reason.substring(0, 40) + '...' : leave.reason}</td>
                <td>${this.formatDate(leave.requestDate)}</td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-success" onclick="leavesManager.approveLeave('${leave.id}')" title="اعتماد">
                            <i class="fas fa-check me-1"></i>اعتماد
                        </button>
                        <button class="btn btn-danger" onclick="leavesManager.rejectLeave('${leave.id}')" title="رفض">
                            <i class="fas fa-times me-1"></i>رفض
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }

    renderLeaveBalanceTable() {
        const tbody = document.getElementById('leaveBalanceTable');
        if (!tbody) return;

        if (this.leaveBalances.length === 0) {
            tbody.innerHTML = '<tr><td colspan="9" class="text-center text-muted">لا توجد بيانات أرصدة</td></tr>';
            return;
        }

        tbody.innerHTML = this.leaveBalances.map(balance => `
            <tr>
                <td>${this.getEmployeeName(balance.employeeId)}</td>
                <td>${this.getEmployeeDepartment(balance.employeeId)}</td>
                <td><span class="badge bg-primary">${balance.annualLeave.total}</span></td>
                <td><span class="badge bg-warning">${balance.annualLeave.used}</span></td>
                <td><span class="badge bg-success">${balance.annualLeave.remaining}</span></td>
                <td><span class="badge bg-primary">${balance.sickLeave.total}</span></td>
                <td><span class="badge bg-warning">${balance.sickLeave.used}</span></td>
                <td><span class="badge bg-success">${balance.sickLeave.remaining}</span></td>
                <td>
                    <button class="btn btn-outline-primary btn-sm" onclick="leavesManager.editBalance('${balance.employeeId}')" title="تعديل الرصيد">
                        <i class="fas fa-edit"></i>
                    </button>
                </td>
            </tr>
        `).join('');
    }

    // Leave Actions
    editLeave(id) {
        const leave = this.leaves.find(l => l.id === id);
        if (!leave) return;

        this.currentEditId = id;

        // Populate form
        document.getElementById('leaveEmployee').value = leave.employeeId;
        document.getElementById('leaveType').value = leave.type;
        document.getElementById('leaveStartDate').value = leave.startDate;
        document.getElementById('leaveEndDate').value = leave.endDate;
        document.getElementById('leaveDays').value = leave.days;
        document.getElementById('leaveReason').value = leave.reason;
        document.getElementById('leaveNotes').value = leave.notes || '';
        document.getElementById('leaveStatus').value = leave.status;
        document.getElementById('leaveWithPay').checked = leave.withPay;

        // Update modal title
        document.getElementById('leaveModalTitle').textContent = 'تعديل طلب الإجازة';

        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('leaveModal'));
        modal.show();
    }

    approveLeave(id) {
        const leave = this.leaves.find(l => l.id === id);
        if (!leave) return;

        // Check balance before approval
        const tempLeave = { ...leave, status: 'approved' };
        if (!this.checkLeaveBalance(tempLeave)) {
            return;
        }

        leave.status = 'approved';
        leave.approvedDate = new Date().toISOString();
        leave.approvedBy = 'المدير'; // You can get this from current user

        // Update balance
        this.updateLeaveBalance(null, leave);

        this.saveToStorage('leaves', this.leaves);
        this.renderAllTables();
        this.updateSummaryCards();

        this.showAlert('تم اعتماد طلب الإجازة بنجاح', 'success');
    }

    rejectLeave(id) {
        const leave = this.leaves.find(l => l.id === id);
        if (!leave) return;

        leave.status = 'rejected';
        leave.rejectedDate = new Date().toISOString();
        leave.rejectedBy = 'المدير'; // You can get this from current user

        this.saveToStorage('leaves', this.leaves);
        this.renderAllTables();
        this.updateSummaryCards();

        this.showAlert('تم رفض طلب الإجازة', 'info');
    }

    deleteLeave(id) {
        this.currentEditId = id;
        const modal = new bootstrap.Modal(document.getElementById('deleteLeaveModal'));
        modal.show();
    }

    confirmDelete() {
        if (!this.currentEditId) return;

        const leaveIndex = this.leaves.findIndex(l => l.id === this.currentEditId);
        if (leaveIndex !== -1) {
            const leave = this.leaves[leaveIndex];

            // Restore balance if leave was approved
            if (leave.status === 'approved') {
                this.updateLeaveBalance(leave, null);
            }

            this.leaves.splice(leaveIndex, 1);
            this.saveToStorage('leaves', this.leaves);
            this.renderAllTables();
            this.updateSummaryCards();
        }

        const modal = bootstrap.Modal.getInstance(document.getElementById('deleteLeaveModal'));
        modal.hide();

        this.showAlert('تم حذف طلب الإجازة بنجاح', 'success');
        this.currentEditId = null;
    }

    editBalance(employeeId) {
        const balance = this.leaveBalances.find(b => b.employeeId === employeeId);
        const employee = this.employees.find(e => e.id === employeeId);

        if (!balance || !employee) return;

        document.getElementById('balanceEmployeeId').value = employeeId;
        document.getElementById('balanceEmployeeName').value = employee.name;
        document.getElementById('annualLeaveBalance').value = balance.annualLeave.total;
        document.getElementById('sickLeaveBalance').value = balance.sickLeave.total;

        const modal = new bootstrap.Modal(document.getElementById('balanceModal'));
        modal.show();
    }

    approveAllPending() {
        const pendingLeaves = this.leaves.filter(leave => leave.status === 'pending');

        if (pendingLeaves.length === 0) {
            this.showAlert('لا توجد طلبات في الانتظار', 'info');
            return;
        }

        const confirmApproval = confirm(`هل تريد اعتماد جميع الطلبات المعلقة (${pendingLeaves.length} طلب)؟`);
        if (!confirmApproval) return;

        let approvedCount = 0;
        let rejectedCount = 0;

        pendingLeaves.forEach(leave => {
            const tempLeave = { ...leave, status: 'approved' };
            if (this.checkLeaveBalance(tempLeave)) {
                leave.status = 'approved';
                leave.approvedDate = new Date().toISOString();
                leave.approvedBy = 'المدير';
                this.updateLeaveBalance(null, leave);
                approvedCount++;
            } else {
                rejectedCount++;
            }
        });

        this.saveToStorage('leaves', this.leaves);
        this.renderAllTables();
        this.updateSummaryCards();

        this.showAlert(`تم اعتماد ${approvedCount} طلب، تم رفض ${rejectedCount} طلب بسبب عدم كفاية الرصيد`, 'success');
    }

    updateAllBalances() {
        // Recalculate all balances based on approved leaves
        this.leaveBalances.forEach(balance => {
            // Reset used amounts
            balance.annualLeave.used = 0;
            balance.sickLeave.used = 0;

            // Calculate used amounts from approved leaves
            const employeeLeaves = this.leaves.filter(l =>
                l.employeeId === balance.employeeId && l.status === 'approved'
            );

            employeeLeaves.forEach(leave => {
                if (leave.type === 'annual') {
                    balance.annualLeave.used += leave.days;
                } else if (leave.type === 'sick') {
                    balance.sickLeave.used += leave.days;
                }
            });

            // Update remaining amounts
            balance.annualLeave.remaining = balance.annualLeave.total - balance.annualLeave.used;
            balance.sickLeave.remaining = balance.sickLeave.total - balance.sickLeave.used;
            balance.lastUpdated = new Date().toISOString();
        });

        this.saveToStorage('leaveBalances', this.leaveBalances);
        this.renderLeaveBalanceTable();
        this.showAlert('تم تحديث جميع أرصدة الإجازات بنجاح', 'success');
    }

    updateSummaryCards() {
        const approvedLeaves = this.leaves.filter(l => l.status === 'approved').length;
        const pendingLeaves = this.leaves.filter(l => l.status === 'pending').length;
        const rejectedLeaves = this.leaves.filter(l => l.status === 'rejected').length;
        const totalDays = this.leaves
            .filter(l => l.status === 'approved')
            .reduce((sum, l) => sum + l.days, 0);

        document.getElementById('totalApprovedLeaves').textContent = approvedLeaves;
        document.getElementById('totalPendingLeaves').textContent = pendingLeaves;
        document.getElementById('totalRejectedLeaves').textContent = rejectedLeaves;
        document.getElementById('totalLeaveDays').textContent = totalDays;
    }

    applyFilters() {
        this.renderAllLeavesTable();
    }

    initializeFilters() {
        // Set current date as default for new leave requests
        const today = new Date().toISOString().split('T')[0];
        document.getElementById('leaveStartDate').value = today;

        const tomorrow = new Date();
        tomorrow.setDate(tomorrow.getDate() + 1);
        document.getElementById('leaveEndDate').value = tomorrow.toISOString().split('T')[0];
    }

    resetLeaveForm() {
        document.getElementById('leaveForm').reset();
        document.getElementById('leaveModalTitle').textContent = 'طلب إجازة جديدة';
        this.initializeFilters();
    }

    generateCalendar() {
        const month = parseInt(document.getElementById('calendarMonth').value);
        const year = parseInt(document.getElementById('calendarYear').value);

        const calendarContainer = document.getElementById('leaveCalendar');

        // Get leaves for the selected month
        const monthLeaves = this.leaves.filter(leave => {
            const startDate = new Date(leave.startDate);
            const endDate = new Date(leave.endDate);
            const targetDate = new Date(year, month - 1, 1);

            return (startDate.getFullYear() === year && startDate.getMonth() + 1 === month) ||
                   (endDate.getFullYear() === year && endDate.getMonth() + 1 === month) ||
                   (startDate <= targetDate && endDate >= targetDate);
        });

        // Generate calendar HTML
        const daysInMonth = new Date(year, month, 0).getDate();
        const firstDay = new Date(year, month - 1, 1).getDay();

        let calendarHTML = `
            <div class="calendar-header">
                <h5>${this.getMonthName(month)} ${year}</h5>
            </div>
            <div class="calendar-grid">
                <div class="calendar-weekdays">
                    <div class="weekday">الأحد</div>
                    <div class="weekday">الاثنين</div>
                    <div class="weekday">الثلاثاء</div>
                    <div class="weekday">الأربعاء</div>
                    <div class="weekday">الخميس</div>
                    <div class="weekday">الجمعة</div>
                    <div class="weekday">السبت</div>
                </div>
                <div class="calendar-days">
        `;

        // Add empty cells for days before the first day of the month
        for (let i = 0; i < firstDay; i++) {
            calendarHTML += '<div class="calendar-day empty"></div>';
        }

        // Add days of the month
        for (let day = 1; day <= daysInMonth; day++) {
            const currentDate = new Date(year, month - 1, day);
            const dayLeaves = monthLeaves.filter(leave => {
                const startDate = new Date(leave.startDate);
                const endDate = new Date(leave.endDate);
                return currentDate >= startDate && currentDate <= endDate && leave.status === 'approved';
            });

            let dayClass = 'calendar-day';
            if (dayLeaves.length > 0) {
                dayClass += ' has-leave';
            }

            calendarHTML += `
                <div class="${dayClass}" title="${dayLeaves.length > 0 ? dayLeaves.map(l => this.getEmployeeName(l.employeeId) + ' - ' + this.leaveTypes[l.type]).join(', ') : ''}">
                    <span class="day-number">${day}</span>
                    ${dayLeaves.length > 0 ? `<span class="leave-count">${dayLeaves.length}</span>` : ''}
                </div>
            `;
        }

        calendarHTML += `
                </div>
            </div>
            <div class="calendar-legend">
                <div class="legend-item">
                    <span class="legend-color has-leave"></span>
                    <span>أيام إجازات</span>
                </div>
            </div>
        `;

        calendarContainer.innerHTML = calendarHTML;
    }

    getMonthName(month) {
        const monthNames = ['', 'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
                           'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'];
        return monthNames[month] || month;
    }

    printLeavesReport() {
        const currentMonth = new Date().getMonth() + 1;
        const currentYear = new Date().getFullYear();

        const printContent = `
            <html>
            <head>
                <title>تقرير الإجازات الشامل</title>
                <style>
                    body {
                        font-family: Arial, sans-serif;
                        direction: rtl;
                        margin: 20px;
                        font-size: 12px;
                    }
                    .header {
                        text-align: center;
                        margin-bottom: 30px;
                        border-bottom: 2px solid #333;
                        padding-bottom: 20px;
                    }
                    .header h1 {
                        color: #333;
                        margin: 0;
                        font-size: 24px;
                    }
                    .summary-grid {
                        display: grid;
                        grid-template-columns: repeat(2, 1fr);
                        gap: 20px;
                        margin: 30px 0;
                    }
                    .summary-card {
                        background: #f8f9fa;
                        padding: 20px;
                        border-radius: 8px;
                        border: 1px solid #dee2e6;
                        text-align: center;
                    }
                    table {
                        width: 100%;
                        border-collapse: collapse;
                        margin: 20px 0;
                        font-size: 11px;
                    }
                    th, td {
                        border: 1px solid #ddd;
                        padding: 8px;
                        text-align: center;
                    }
                    th {
                        background-color: #f8f9fa;
                        font-weight: bold;
                    }
                    .section-title {
                        background: #007bff;
                        color: white;
                        padding: 10px;
                        margin: 20px 0 10px 0;
                        border-radius: 5px;
                        text-align: center;
                        font-weight: bold;
                    }
                    @media print {
                        body { margin: 0; }
                    }
                </style>
            </head>
            <body>
                <div class="header">
                    <h1>تقرير الإجازات الشامل</h1>
                    <p>تاريخ التقرير: ${new Date().toLocaleDateString('ar-SA')}</p>
                    <p>وقت الإنشاء: ${new Date().toLocaleTimeString('ar-SA')}</p>
                </div>

                <div class="summary-grid">
                    <div class="summary-card">
                        <h3>الإجازات المعتمدة</h3>
                        <div class="amount">${this.leaves.filter(l => l.status === 'approved').length}</div>
                    </div>
                    <div class="summary-card">
                        <h3>في انتظار الموافقة</h3>
                        <div class="amount">${this.leaves.filter(l => l.status === 'pending').length}</div>
                    </div>
                    <div class="summary-card">
                        <h3>الإجازات المرفوضة</h3>
                        <div class="amount">${this.leaves.filter(l => l.status === 'rejected').length}</div>
                    </div>
                    <div class="summary-card">
                        <h3>إجمالي أيام الإجازات</h3>
                        <div class="amount">${this.leaves.filter(l => l.status === 'approved').reduce((sum, l) => sum + l.days, 0)}</div>
                    </div>
                </div>

                <div class="section-title">جميع طلبات الإجازات</div>
                <table>
                    <thead>
                        <tr>
                            <th>الموظف</th>
                            <th>نوع الإجازة</th>
                            <th>تاريخ البداية</th>
                            <th>تاريخ النهاية</th>
                            <th>عدد الأيام</th>
                            <th>الحالة</th>
                            <th>السبب</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${this.leaves.map(leave => `
                            <tr>
                                <td>${this.getEmployeeName(leave.employeeId)}</td>
                                <td>${this.leaveTypes[leave.type]}</td>
                                <td>${this.formatDate(leave.startDate)}</td>
                                <td>${this.formatDate(leave.endDate)}</td>
                                <td>${leave.days}</td>
                                <td>${this.statusTypes[leave.status]}</td>
                                <td>${leave.reason}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>

                <div class="section-title">أرصدة الإجازات</div>
                <table>
                    <thead>
                        <tr>
                            <th>الموظف</th>
                            <th>القسم</th>
                            <th>الإجازة السنوية (المتاح)</th>
                            <th>الإجازة السنوية (المستخدم)</th>
                            <th>الإجازة المرضية (المتاح)</th>
                            <th>الإجازة المرضية (المستخدم)</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${this.leaveBalances.map(balance => `
                            <tr>
                                <td>${this.getEmployeeName(balance.employeeId)}</td>
                                <td>${this.getEmployeeDepartment(balance.employeeId)}</td>
                                <td>${balance.annualLeave.remaining}</td>
                                <td>${balance.annualLeave.used}</td>
                                <td>${balance.sickLeave.remaining}</td>
                                <td>${balance.sickLeave.used}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>

                <div style="margin-top: 40px; text-align: center; font-size: 10px; color: #666;">
                    <p>تم إنشاء هذا التقرير بواسطة نظام إدارة الموظفين</p>
                    <p>جميع الحقوق محفوظة © ${new Date().getFullYear()}</p>
                </div>
            </body>
            </html>
        `;

        const printWindow = window.open('', '_blank');
        printWindow.document.write(printContent);
        printWindow.document.close();
        printWindow.print();
    }
}

// Global instance
const leavesManager = new LeavesManager();

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', async function() {
    console.log('DOM loaded, initializing leaves manager...');
    await leavesManager.init();
    console.log('Leaves manager initialized successfully');
});

// Refresh data when page becomes visible
document.addEventListener('visibilitychange', function() {
    if (!document.hidden) {
        console.log('Page became visible, refreshing data...');
        leavesManager.populateEmployeeDropdowns();
    }
});
