<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الإدارة المالية - نظام إدارة الموظفين</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link href="styles.css" rel="stylesheet">
</head>
<body>
    <!-- Sidebar -->
    <nav class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <div class="sidebar-brand">
                <i class="fas fa-building"></i>
                <span class="sidebar-text">إدارة الموظفين</span>
            </div>
        </div>
        <ul class="sidebar-nav">
            <li>
                <a href="dashboard.html">
                    <i class="fas fa-tachometer-alt"></i>
                    <span class="sidebar-text">لوحة التحكم</span>
                </a>
            </li>
            <li>
                <a href="employees.html">
                    <i class="fas fa-users"></i>
                    <span class="sidebar-text">الموظفين</span>
                </a>
            </li>
            <li>
                <a href="payroll.html">
                    <i class="fas fa-money-bill-wave"></i>
                    <span class="sidebar-text">الرواتب</span>
                </a>
            </li>
            <li class="active">
                <a href="financial-management.html">
                    <i class="fas fa-coins"></i>
                    <span class="sidebar-text">الإدارة المالية</span>
                </a>
            </li>
            <li>
                <a href="reports.html">
                    <i class="fas fa-chart-bar"></i>
                    <span class="sidebar-text">التقارير</span>
                </a>
            </li>
            <li>
                <a href="attendance.html">
                    <i class="fas fa-clock"></i>
                    <span class="sidebar-text">الحضور والانصراف</span>
                </a>
            </li>
            <li>
                <a href="settings.html">
                    <i class="fas fa-cog"></i>
                    <span class="sidebar-text">الإعدادات</span>
                </a>
            </li>
            <li>
                <a href="#" class="logout-btn">
                    <i class="fas fa-sign-out-alt"></i>
                    <span class="sidebar-text">تسجيل الخروج</span>
                </a>
            </li>
        </ul>
    </nav>

    <!-- Main Content -->
    <div class="main-content" id="mainContent">
        <!-- Header -->
        <header class="header">
            <div class="d-flex align-items-center">
                <button class="btn btn-link text-dark me-3" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h3 class="header-title">الإدارة المالية</h3>
            </div>
            <div class="d-flex align-items-center">
                <button class="btn btn-outline-primary me-3" onclick="financialManager.printFinancialReport()" title="طباعة التقرير المالي">
                    <i class="fas fa-print me-1"></i>
                    طباعة
                </button>
                <div class="user-info">
                    <span class="user-name me-2"></span>
                    <span class="user-role badge bg-primary me-2"></span>
                    <div class="user-avatar"></div>
                </div>
            </div>
        </header>

        <!-- Content Area -->
        <div class="content-area">
            <!-- Financial Summary Cards -->
            <div class="row mb-4">
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="stat-card success fade-in">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="stat-number" id="totalAllowances">0 ر.س</h3>
                                <p class="stat-label">إجمالي العلاوات</p>
                                <div class="progress mt-2" style="height: 4px;">
                                    <div class="progress-bar bg-success" role="progressbar" style="width: 75%"></div>
                                </div>
                            </div>
                            <div class="stat-icon">
                                <i class="fas fa-arrow-up fa-2x text-success"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="stat-card danger slide-in-right">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="stat-number" id="totalPenalties">0 ر.س</h3>
                                <p class="stat-label">إجمالي الغرامات</p>
                                <div class="progress mt-2" style="height: 4px;">
                                    <div class="progress-bar bg-danger" role="progressbar" style="width: 60%"></div>
                                </div>
                            </div>
                            <div class="stat-icon">
                                <i class="fas fa-arrow-down fa-2x text-danger"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="stat-card warning bounce-in">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="stat-number" id="totalLoans">0 ر.س</h3>
                                <p class="stat-label">إجمالي القروض</p>
                                <div class="progress mt-2" style="height: 4px;">
                                    <div class="progress-bar bg-warning" role="progressbar" style="width: 85%"></div>
                                </div>
                            </div>
                            <div class="stat-icon">
                                <i class="fas fa-hand-holding-usd fa-2x text-warning"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="stat-card info slide-in-left">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="stat-number" id="netFinancialImpact">0 ر.س</h3>
                                <p class="stat-label">صافي التأثير المالي</p>
                                <div class="progress mt-2" style="height: 4px;">
                                    <div class="progress-bar bg-info" role="progressbar" style="width: 70%"></div>
                                </div>
                            </div>
                            <div class="stat-icon">
                                <i class="fas fa-balance-scale fa-2x text-info"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Financial Management Tabs -->
            <div class="row">
                <div class="col-12">
                    <div class="table-container">
                        <div class="table-header">
                            <h5 class="mb-0">إدارة العمليات المالية</h5>
                        </div>
                        
                        <!-- Navigation Tabs -->
                        <ul class="nav nav-tabs financial-tabs" id="financialTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="allowances-tab" data-bs-toggle="tab" data-bs-target="#allowances" type="button" role="tab">
                                    <i class="fas fa-plus-circle me-2"></i>العلاوات
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="penalties-tab" data-bs-toggle="tab" data-bs-target="#penalties" type="button" role="tab">
                                    <i class="fas fa-minus-circle me-2"></i>الغرامات
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="loans-tab" data-bs-toggle="tab" data-bs-target="#loans" type="button" role="tab">
                                    <i class="fas fa-hand-holding-usd me-2"></i>القروض
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="summary-tab" data-bs-toggle="tab" data-bs-target="#summary" type="button" role="tab">
                                    <i class="fas fa-chart-pie me-2"></i>الملخص
                                </button>
                            </li>
                        </ul>

                        <!-- Tab Content -->
                        <div class="tab-content financial-tab-content" id="financialTabContent">
                            <!-- Allowances Tab -->
                            <div class="tab-pane fade show active" id="allowances" role="tabpanel">
                                <div class="financial-section">
                                    <div class="section-header">
                                        <h6>إدارة العلاوات</h6>
                                        <button class="btn btn-success btn-sm" data-bs-toggle="modal" data-bs-target="#allowanceModal">
                                            <i class="fas fa-plus me-1"></i>إضافة علاوة
                                        </button>
                                    </div>
                                    <div class="table-responsive">
                                        <table class="table table-hover">
                                            <thead>
                                                <tr>
                                                    <th>الموظف</th>
                                                    <th>نوع العلاوة</th>
                                                    <th>المبلغ</th>
                                                    <th>التاريخ</th>
                                                    <th>الحالة</th>
                                                    <th>الإجراءات</th>
                                                </tr>
                                            </thead>
                                            <tbody id="allowancesTable">
                                                <tr>
                                                    <td colspan="6" class="text-center text-muted">لا توجد علاوات مسجلة</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>

                            <!-- Penalties Tab -->
                            <div class="tab-pane fade" id="penalties" role="tabpanel">
                                <div class="financial-section">
                                    <div class="section-header">
                                        <h6>إدارة الغرامات</h6>
                                        <button class="btn btn-danger btn-sm" data-bs-toggle="modal" data-bs-target="#penaltyModal">
                                            <i class="fas fa-plus me-1"></i>إضافة غرامة
                                        </button>
                                    </div>
                                    <div class="table-responsive">
                                        <table class="table table-hover">
                                            <thead>
                                                <tr>
                                                    <th>الموظف</th>
                                                    <th>نوع الغرامة</th>
                                                    <th>المبلغ</th>
                                                    <th>التاريخ</th>
                                                    <th>السبب</th>
                                                    <th>الحالة</th>
                                                    <th>الإجراءات</th>
                                                </tr>
                                            </thead>
                                            <tbody id="penaltiesTable">
                                                <tr>
                                                    <td colspan="7" class="text-center text-muted">لا توجد غرامات مسجلة</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>

                            <!-- Loans Tab -->
                            <div class="tab-pane fade" id="loans" role="tabpanel">
                                <div class="financial-section">
                                    <div class="section-header">
                                        <h6>إدارة القروض</h6>
                                        <button class="btn btn-warning btn-sm" data-bs-toggle="modal" data-bs-target="#loanModal">
                                            <i class="fas fa-plus me-1"></i>إضافة قرض
                                        </button>
                                    </div>
                                    <div class="table-responsive">
                                        <table class="table table-hover">
                                            <thead>
                                                <tr>
                                                    <th>الموظف</th>
                                                    <th>مبلغ القرض</th>
                                                    <th>المبلغ المسدد</th>
                                                    <th>المتبقي</th>
                                                    <th>القسط الشهري</th>
                                                    <th>تاريخ البداية</th>
                                                    <th>الحالة</th>
                                                    <th>الإجراءات</th>
                                                </tr>
                                            </thead>
                                            <tbody id="loansTable">
                                                <tr>
                                                    <td colspan="8" class="text-center text-muted">لا توجد قروض مسجلة</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>

                            <!-- Summary Tab -->
                            <div class="tab-pane fade" id="summary" role="tabpanel">
                                <div class="financial-section">
                                    <div class="section-header">
                                        <h6>الملخص المالي</h6>
                                        <button class="btn btn-info btn-sm" onclick="financialManager.refreshSummary()">
                                            <i class="fas fa-sync-alt me-1"></i>تحديث
                                        </button>
                                    </div>
                                    <div class="row">
                                        <div class="col-lg-8">
                                            <canvas id="financialChart" height="100"></canvas>
                                        </div>
                                        <div class="col-lg-4">
                                            <div class="summary-stats">
                                                <div class="summary-item">
                                                    <span class="label">العلاوات النشطة</span>
                                                    <span class="value text-success" id="activeAllowances">0</span>
                                                </div>
                                                <div class="summary-item">
                                                    <span class="label">الغرامات المطبقة</span>
                                                    <span class="value text-danger" id="activePenalties">0</span>
                                                </div>
                                                <div class="summary-item">
                                                    <span class="label">القروض النشطة</span>
                                                    <span class="value text-warning" id="activeLoans">0</span>
                                                </div>
                                                <div class="summary-item">
                                                    <span class="label">إجمالي الأقساط الشهرية</span>
                                                    <span class="value text-info" id="totalMonthlyInstallments">0 ر.س</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Allowance Modal -->
    <div class="modal fade" id="allowanceModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-success text-white">
                    <h5 class="modal-title">
                        <i class="fas fa-plus-circle me-2"></i>
                        <span id="allowanceModalTitle">إضافة علاوة جديدة</span>
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="allowanceForm">
                        <input type="hidden" id="allowanceId">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="allowanceEmployee" class="form-label">الموظف *</label>
                                <select class="form-select" id="allowanceEmployee" required>
                                    <option value="">اختر الموظف</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="allowanceType" class="form-label">نوع العلاوة *</label>
                                <select class="form-select" id="allowanceType" required>
                                    <option value="">اختر نوع العلاوة</option>
                                    <option value="performance">علاوة أداء</option>
                                    <option value="promotion">علاوة ترقية</option>
                                    <option value="overtime">علاوة ساعات إضافية</option>
                                    <option value="transport">بدل مواصلات</option>
                                    <option value="housing">بدل سكن</option>
                                    <option value="food">بدل طعام</option>
                                    <option value="special">علاوة خاصة</option>
                                    <option value="annual">علاوة سنوية</option>
                                </select>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="allowanceAmount" class="form-label">المبلغ (ريال) *</label>
                                <input type="number" class="form-control" id="allowanceAmount" step="0.01" min="0" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="allowanceDate" class="form-label">تاريخ العلاوة *</label>
                                <input type="date" class="form-control" id="allowanceDate" required>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="allowanceFrequency" class="form-label">تكرار العلاوة *</label>
                                <select class="form-select" id="allowanceFrequency" required>
                                    <option value="monthly">شهرية</option>
                                    <option value="quarterly">ربع سنوية</option>
                                    <option value="annual">سنوية</option>
                                    <option value="one-time">مرة واحدة</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="allowanceStatus" class="form-label">الحالة *</label>
                                <select class="form-select" id="allowanceStatus" required>
                                    <option value="active">نشطة</option>
                                    <option value="pending">في الانتظار</option>
                                    <option value="suspended">معلقة</option>
                                    <option value="cancelled">ملغية</option>
                                </select>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="allowanceNotes" class="form-label">ملاحظات</label>
                            <textarea class="form-control" id="allowanceNotes" rows="3" placeholder="أدخل أي ملاحظات إضافية..."></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-success" id="saveAllowanceBtn">
                        <i class="fas fa-save me-1"></i>حفظ العلاوة
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Penalty Modal -->
    <div class="modal fade" id="penaltyModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title">
                        <i class="fas fa-minus-circle me-2"></i>
                        <span id="penaltyModalTitle">إضافة غرامة جديدة</span>
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="penaltyForm">
                        <input type="hidden" id="penaltyId">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="penaltyEmployee" class="form-label">الموظف *</label>
                                <select class="form-select" id="penaltyEmployee" required>
                                    <option value="">اختر الموظف</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="penaltyType" class="form-label">نوع الغرامة *</label>
                                <select class="form-select" id="penaltyType" required>
                                    <option value="">اختر نوع الغرامة</option>
                                    <option value="late">غرامة تأخير</option>
                                    <option value="absence">غرامة غياب</option>
                                    <option value="violation">مخالفة نظام</option>
                                    <option value="damage">تعويض أضرار</option>
                                    <option value="disciplinary">إجراء تأديبي</option>
                                    <option value="other">أخرى</option>
                                </select>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="penaltyAmount" class="form-label">المبلغ (ريال) *</label>
                                <input type="number" class="form-control" id="penaltyAmount" step="0.01" min="0" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="penaltyDate" class="form-label">تاريخ الغرامة *</label>
                                <input type="date" class="form-control" id="penaltyDate" required>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="penaltyDeductionType" class="form-label">طريقة الخصم *</label>
                                <select class="form-select" id="penaltyDeductionType" required>
                                    <option value="immediate">خصم فوري</option>
                                    <option value="installments">أقساط شهرية</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3" id="installmentsField" style="display: none;">
                                <label for="penaltyInstallments" class="form-label">عدد الأقساط</label>
                                <input type="number" class="form-control" id="penaltyInstallments" min="1" max="12">
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="penaltyReason" class="form-label">سبب الغرامة *</label>
                            <textarea class="form-control" id="penaltyReason" rows="3" required placeholder="أدخل سبب الغرامة بالتفصيل..."></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="penaltyStatus" class="form-label">الحالة *</label>
                            <select class="form-select" id="penaltyStatus" required>
                                <option value="active">نشطة</option>
                                <option value="pending">في الانتظار</option>
                                <option value="paid">مدفوعة</option>
                                <option value="cancelled">ملغية</option>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-danger" id="savePenaltyBtn">
                        <i class="fas fa-save me-1"></i>حفظ الغرامة
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Loan Modal -->
    <div class="modal fade" id="loanModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-warning text-dark">
                    <h5 class="modal-title">
                        <i class="fas fa-hand-holding-usd me-2"></i>
                        <span id="loanModalTitle">إضافة قرض جديد</span>
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="loanForm">
                        <input type="hidden" id="loanId">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="loanEmployee" class="form-label">الموظف *</label>
                                <select class="form-select" id="loanEmployee" required>
                                    <option value="">اختر الموظف</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="loanType" class="form-label">نوع القرض *</label>
                                <select class="form-select" id="loanType" required>
                                    <option value="">اختر نوع القرض</option>
                                    <option value="personal">قرض شخصي</option>
                                    <option value="emergency">قرض طوارئ</option>
                                    <option value="housing">قرض سكن</option>
                                    <option value="car">قرض سيارة</option>
                                    <option value="education">قرض تعليم</option>
                                    <option value="medical">قرض طبي</option>
                                    <option value="advance">سلفة راتب</option>
                                </select>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="loanAmount" class="form-label">مبلغ القرض (ريال) *</label>
                                <input type="number" class="form-control" id="loanAmount" step="0.01" min="0" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="loanDate" class="form-label">تاريخ القرض *</label>
                                <input type="date" class="form-control" id="loanDate" required>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="loanDuration" class="form-label">مدة السداد (شهر) *</label>
                                <input type="number" class="form-control" id="loanDuration" min="1" max="60" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="loanInterestRate" class="form-label">معدل الفائدة (%)</label>
                                <input type="number" class="form-control" id="loanInterestRate" step="0.01" min="0" max="100" value="0">
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="monthlyInstallment" class="form-label">القسط الشهري (ريال)</label>
                                <input type="number" class="form-control" id="monthlyInstallment" step="0.01" readonly>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="loanStatus" class="form-label">الحالة *</label>
                                <select class="form-select" id="loanStatus" required>
                                    <option value="active">نشط</option>
                                    <option value="pending">في الانتظار</option>
                                    <option value="completed">مكتمل</option>
                                    <option value="cancelled">ملغي</option>
                                </select>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="loanPurpose" class="form-label">الغرض من القرض *</label>
                            <textarea class="form-control" id="loanPurpose" rows="3" required placeholder="أدخل الغرض من القرض..."></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="loanNotes" class="form-label">ملاحظات</label>
                            <textarea class="form-control" id="loanNotes" rows="2" placeholder="أدخل أي ملاحظات إضافية..."></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-warning" id="saveLoanBtn">
                        <i class="fas fa-save me-1"></i>حفظ القرض
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteFinancialModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title">تأكيد الحذف</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>هل أنت متأكد من حذف هذا العنصر؟</p>
                    <p class="text-muted">لا يمكن التراجع عن هذا الإجراء.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-danger" id="confirmDeleteFinancialBtn">
                        <i class="fas fa-trash me-1"></i>حذف
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="database.js"></script>
    <script src="auth.js"></script>
    <script src="app.js"></script>
    <script src="financial-management.js"></script>
</body>
</html>
