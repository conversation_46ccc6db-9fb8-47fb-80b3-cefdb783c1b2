// Main Application Controller
class EmployeeManagementApp {
    constructor() {
        this.currentPage = '';
        this.isInitialized = false;
    }

    async init() {
        if (this.isInitialized) return;

        try {
            // Initialize database
            await initializeDatabase();
            
            // Setup global event listeners
            this.setupGlobalEvents();
            
            // Initialize page-specific functionality
            this.initializePage();
            
            this.isInitialized = true;
            console.log('Employee Management App initialized successfully');
            
        } catch (error) {
            console.error('Failed to initialize app:', error);
            this.showGlobalError('فشل في تهيئة النظام');
        }
    }

    setupGlobalEvents() {
        // Handle network status
        window.addEventListener('online', () => {
            this.showNotification('تم استعادة الاتصال', 'success');
        });

        window.addEventListener('offline', () => {
            this.showNotification('لا يوجد اتصال بالإنترنت - النظام يعمل محلياً', 'warning');
        });

        // Handle page visibility
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                console.log('Page hidden - pausing updates');
            } else {
                console.log('Page visible - resuming updates');
                this.refreshCurrentPage();
            }
        });

        // Global keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            // Ctrl+S to save (prevent default browser save)
            if (e.ctrlKey && e.key === 's') {
                e.preventDefault();
                this.handleGlobalSave();
            }
            
            // Escape to close modals
            if (e.key === 'Escape') {
                this.closeAllModals();
            }
        });
    }

    initializePage() {
        const path = window.location.pathname;
        const page = path.split('/').pop() || 'index.html';
        
        this.currentPage = page.replace('.html', '');
        
        // Add page-specific classes
        document.body.classList.add(`page-${this.currentPage}`);
        
        // Initialize page-specific features
        switch (this.currentPage) {
            case 'index':
                this.initLoginPage();
                break;
            case 'dashboard':
                this.initDashboardPage();
                break;
            case 'employees':
                this.initEmployeesPage();
                break;
            case 'payroll':
                this.initPayrollPage();
                break;
            case 'reports':
                this.initReportsPage();
                break;
            case 'attendance':
                this.initAttendancePage();
                break;
            case 'settings':
                this.initSettingsPage();
                break;
        }
    }

    initLoginPage() {
        // Add login page specific enhancements
        this.addLoginAnimations();
    }

    initDashboardPage() {
        // Add dashboard specific enhancements
        this.addDashboardAnimations();
    }

    initEmployeesPage() {
        // Add employees page specific enhancements
        this.addTableEnhancements();
    }

    initPayrollPage() {
        // Add payroll page specific enhancements
        this.addCalculatorEnhancements();
    }

    initReportsPage() {
        // Add reports page specific enhancements
        this.addChartEnhancements();
    }

    initAttendancePage() {
        // Add attendance page specific enhancements
        this.addTimeTrackingEnhancements();
    }

    initSettingsPage() {
        // Add settings page specific enhancements
        this.addSettingsEnhancements();
    }

    addLoginAnimations() {
        // Animate login form elements
        const formElements = document.querySelectorAll('.login-form-container .form-control, .login-form-container .btn');
        formElements.forEach((element, index) => {
            element.style.animationDelay = `${index * 0.1}s`;
            element.classList.add('fade-in');
        });
    }

    addDashboardAnimations() {
        // Animate dashboard cards
        const cards = document.querySelectorAll('.stat-card');
        cards.forEach((card, index) => {
            card.style.animationDelay = `${index * 0.1}s`;
        });
    }

    addTableEnhancements() {
        // Add table row hover effects and animations
        const tables = document.querySelectorAll('.table tbody tr');
        tables.forEach(row => {
            row.addEventListener('mouseenter', () => {
                row.style.transform = 'scale(1.01)';
            });
            
            row.addEventListener('mouseleave', () => {
                row.style.transform = 'scale(1)';
            });
        });
    }

    addCalculatorEnhancements() {
        // Add calculator-like animations for payroll
        const numberInputs = document.querySelectorAll('input[type="number"]');
        numberInputs.forEach(input => {
            input.addEventListener('focus', () => {
                input.parentElement.classList.add('focused');
            });
            
            input.addEventListener('blur', () => {
                input.parentElement.classList.remove('focused');
            });
        });
    }

    addChartEnhancements() {
        // Add chart loading animations
        const chartContainers = document.querySelectorAll('canvas');
        chartContainers.forEach(canvas => {
            canvas.style.opacity = '0';
            canvas.style.transition = 'opacity 0.5s ease';
            
            // Fade in when chart is ready
            setTimeout(() => {
                canvas.style.opacity = '1';
            }, 500);
        });
    }

    addTimeTrackingEnhancements() {
        // Add real-time clock updates
        this.updateClock();
        setInterval(() => this.updateClock(), 1000);
    }

    addSettingsEnhancements() {
        // Add settings form validation and animations
        const settingsForms = document.querySelectorAll('form');
        settingsForms.forEach(form => {
            form.addEventListener('submit', (e) => {
                this.animateFormSubmission(form);
            });
        });
    }

    updateClock() {
        const clockElement = document.getElementById('currentTime');
        if (clockElement) {
            const now = new Date();
            clockElement.textContent = now.toLocaleTimeString('ar-SA');
        }
    }

    animateFormSubmission(form) {
        const submitBtn = form.querySelector('button[type="submit"]');
        if (submitBtn) {
            submitBtn.classList.add('loading');
            setTimeout(() => {
                submitBtn.classList.remove('loading');
            }, 2000);
        }
    }

    handleGlobalSave() {
        // Handle global save shortcut
        const activeForm = document.querySelector('form:focus-within');
        if (activeForm) {
            const submitBtn = activeForm.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.click();
            }
        }
    }

    closeAllModals() {
        // Close all open modals
        const modals = document.querySelectorAll('.modal.show');
        modals.forEach(modal => {
            const modalInstance = bootstrap.Modal.getInstance(modal);
            if (modalInstance) {
                modalInstance.hide();
            }
        });
    }

    refreshCurrentPage() {
        // Refresh current page data
        switch (this.currentPage) {
            case 'dashboard':
                if (window.dashboard) {
                    dashboard.refreshData();
                }
                break;
            case 'employees':
                if (window.employeeManager) {
                    employeeManager.loadData();
                }
                break;
            // Add other pages as needed
        }
    }

    showNotification(message, type = 'info') {
        // Create and show notification
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(notification);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }

    showGlobalError(message) {
        // Show global error message
        const errorDiv = document.createElement('div');
        errorDiv.className = 'alert alert-danger position-fixed w-100';
        errorDiv.style.cssText = 'top: 0; left: 0; z-index: 9999; border-radius: 0;';
        errorDiv.innerHTML = `
            <div class="container">
                <strong>خطأ في النظام:</strong> ${message}
                <button type="button" class="btn-close float-end" onclick="this.parentElement.parentElement.remove()"></button>
            </div>
        `;

        document.body.insertBefore(errorDiv, document.body.firstChild);
    }
}

// Ensure employees data is available globally
function ensureEmployeesData() {
    const employeesData = localStorage.getItem('employees');
    if (!employeesData || JSON.parse(employeesData).length === 0) {
        console.log('Creating sample employees data...');
        const sampleEmployees = [
            {
                id: '1',
                name: 'أحمد محمد علي',
                employeeNumber: 'EMP001',
                department: 'تقنية المعلومات',
                position: 'مطور برمجيات',
                salary: 8000,
                email: '<EMAIL>',
                phone: '0501234567',
                hireDate: '2023-01-15',
                status: 'active'
            },
            {
                id: '2',
                name: 'فاطمة أحمد السالم',
                employeeNumber: 'EMP002',
                department: 'الموارد البشرية',
                position: 'أخصائي موارد بشرية',
                salary: 6500,
                email: '<EMAIL>',
                phone: '0507654321',
                hireDate: '2023-02-01',
                status: 'active'
            },
            {
                id: '3',
                name: 'محمد عبدالله الأحمد',
                employeeNumber: 'EMP003',
                department: 'المالية',
                position: 'محاسب',
                salary: 7000,
                email: '<EMAIL>',
                phone: '0509876543',
                hireDate: '2023-03-10',
                status: 'active'
            },
            {
                id: '4',
                name: 'نورا سعد المطيري',
                employeeNumber: 'EMP004',
                department: 'التسويق',
                position: 'أخصائي تسويق',
                salary: 5500,
                email: '<EMAIL>',
                phone: '0502468135',
                hireDate: '2023-04-05',
                status: 'active'
            },
            {
                id: '5',
                name: 'خالد عبدالرحمن القحطاني',
                employeeNumber: 'EMP005',
                department: 'المبيعات',
                position: 'مندوب مبيعات',
                salary: 6000,
                email: '<EMAIL>',
                phone: '0503691472',
                hireDate: '2023-05-20',
                status: 'active'
            }
        ];

        localStorage.setItem('employees', JSON.stringify(sampleEmployees));
        console.log('Sample employees data created and saved');
    }
}

// Initialize app when DOM is loaded
const app = new EmployeeManagementApp();

document.addEventListener('DOMContentLoaded', () => {
    ensureEmployeesData();
    app.init();
});

// Make app globally available
window.employeeApp = app;
