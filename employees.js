// Employee Management
class EmployeeManager {
    constructor() {
        this.employees = [];
        this.filteredEmployees = [];
        this.departments = [];
        this.positions = [];
        this.currentPage = 1;
        this.itemsPerPage = 10;
        this.editingEmployeeId = null;
    }

    async init() {
        await this.loadData();
        this.setupEventListeners();
        this.renderEmployees();
        this.setupSidebar();
    }

    async loadData() {
        try {
            this.employees = await dbManager.getAll('employees');
            this.departments = await dbManager.getAll('departments');
            this.positions = await dbManager.getAll('positions');
            this.filteredEmployees = [...this.employees];
            
            this.populateDropdowns();
        } catch (error) {
            console.error('Error loading data:', error);
            this.showAlert('حدث خطأ في تحميل البيانات', 'danger');
        }
    }

    populateDropdowns() {
        // Populate department filters and form selects
        const departmentFilter = document.getElementById('departmentFilter');
        const departmentSelect = document.getElementById('department');
        
        departmentFilter.innerHTML = '<option value="">جميع الأقسام</option>';
        departmentSelect.innerHTML = '<option value="">اختر القسم</option>';
        
        this.departments.forEach(dept => {
            departmentFilter.innerHTML += `<option value="${dept.name}">${dept.name}</option>`;
            departmentSelect.innerHTML += `<option value="${dept.name}">${dept.name}</option>`;
        });

        // Populate position select
        const positionSelect = document.getElementById('position');
        positionSelect.innerHTML = '<option value="">اختر الوظيفة</option>';
        
        this.positions.forEach(pos => {
            positionSelect.innerHTML += `<option value="${pos.title}" data-department="${pos.department}">${pos.title}</option>`;
        });
    }

    setupEventListeners() {
        // Search functionality
        const searchInput = document.getElementById('searchInput');
        const searchBtn = document.getElementById('searchBtn');
        
        searchInput.addEventListener('input', () => this.applyFilters());
        searchBtn.addEventListener('click', () => this.applyFilters());

        // Filter functionality
        document.getElementById('departmentFilter').addEventListener('change', () => this.applyFilters());
        document.getElementById('statusFilter').addEventListener('change', () => this.applyFilters());
        document.getElementById('clearFilters').addEventListener('click', () => this.clearFilters());

        // Add employee button
        document.getElementById('addEmployeeBtn').addEventListener('click', () => this.showEmployeeModal());

        // Export buttons
        document.getElementById('exportPdfBtn').addEventListener('click', () => this.exportEmployees());
        document.getElementById('exportExcelBtn').addEventListener('click', () => this.exportToExcel());
        document.getElementById('printBtn').addEventListener('click', () => this.printEmployees());

        // Select all checkbox
        document.getElementById('selectAll').addEventListener('change', (e) => this.selectAllEmployees(e.target.checked));

        // Employee form
        document.getElementById('employeeForm').addEventListener('submit', (e) => this.saveEmployee(e));

        // Department change in form
        document.getElementById('department').addEventListener('change', (e) => this.filterPositions(e.target.value));

        // Delete confirmation
        document.getElementById('confirmDeleteBtn').addEventListener('click', () => this.deleteEmployee());

        // Document management initialization
        this.initializeDocumentManager();
    }

    setupSidebar() {
        const sidebarToggle = document.getElementById('sidebarToggle');
        const sidebar = document.getElementById('sidebar');
        const mainContent = document.getElementById('mainContent');

        if (sidebarToggle) {
            sidebarToggle.addEventListener('click', () => {
                sidebar.classList.toggle('collapsed');
                mainContent.classList.toggle('expanded');
            });
        }
    }

    applyFilters() {
        const searchTerm = document.getElementById('searchInput').value.toLowerCase();
        const departmentFilter = document.getElementById('departmentFilter').value;
        const statusFilter = document.getElementById('statusFilter').value;

        this.filteredEmployees = this.employees.filter(employee => {
            const matchesSearch = !searchTerm || 
                employee.name.toLowerCase().includes(searchTerm) ||
                employee.employeeNumber.toLowerCase().includes(searchTerm);
            
            const matchesDepartment = !departmentFilter || employee.department === departmentFilter;
            const matchesStatus = !statusFilter || employee.status === statusFilter;

            return matchesSearch && matchesDepartment && matchesStatus;
        });

        this.currentPage = 1;
        this.renderEmployees();
    }

    clearFilters() {
        document.getElementById('searchInput').value = '';
        document.getElementById('departmentFilter').value = '';
        document.getElementById('statusFilter').value = '';
        
        this.filteredEmployees = [...this.employees];
        this.currentPage = 1;
        this.renderEmployees();
    }

    renderEmployees() {
        const tbody = document.getElementById('employeesTableBody');
        const recordsInfo = document.getElementById('recordsInfo');
        
        if (this.filteredEmployees.length === 0) {
            tbody.innerHTML = '<tr><td colspan="9" class="text-center text-muted">لا توجد بيانات</td></tr>';
            recordsInfo.textContent = 'عرض 0 من 0 سجل';
            this.renderPagination();
            return;
        }

        // Calculate pagination
        const startIndex = (this.currentPage - 1) * this.itemsPerPage;
        const endIndex = Math.min(startIndex + this.itemsPerPage, this.filteredEmployees.length);
        const pageEmployees = this.filteredEmployees.slice(startIndex, endIndex);

        // Render table rows
        tbody.innerHTML = pageEmployees.map(employee => `
            <tr>
                <td>
                    <input type="checkbox" class="form-check-input employee-checkbox" value="${employee.id}">
                </td>
                <td>${employee.employeeNumber}</td>
                <td>
                    <div class="d-flex align-items-center">
                        <div class="user-avatar me-2" style="width: 30px; height: 30px; font-size: 0.8rem;">
                            ${employee.name.charAt(0)}
                        </div>
                        ${employee.name}
                    </div>
                </td>
                <td>${employee.department}</td>
                <td>${employee.position}</td>
                <td>${this.formatCurrency(employee.salary)}</td>
                <td>${this.formatDate(employee.hireDate)}</td>
                <td>
                    <span class="status-badge ${this.getStatusClass(employee.status)}">
                        ${this.getStatusText(employee.status)}
                    </span>
                </td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="employeeManager.editEmployee(${employee.id})" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-outline-info" onclick="employeeManager.viewEmployee(${employee.id})" title="عرض">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="employeeManager.confirmDeleteEmployee(${employee.id})" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');

        // Update records info
        recordsInfo.textContent = `عرض ${startIndex + 1} إلى ${endIndex} من ${this.filteredEmployees.length} سجل`;

        // Update table footer
        this.updateTableFooter();

        // Render pagination
        this.renderPagination();
    }

    renderPagination() {
        const pagination = document.getElementById('pagination');
        const totalPages = Math.ceil(this.filteredEmployees.length / this.itemsPerPage);

        if (totalPages <= 1) {
            pagination.innerHTML = '';
            return;
        }

        let paginationHTML = '';

        // Previous button
        paginationHTML += `
            <li class="page-item ${this.currentPage === 1 ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="employeeManager.goToPage(${this.currentPage - 1})">السابق</a>
            </li>
        `;

        // Page numbers
        for (let i = 1; i <= totalPages; i++) {
            if (i === 1 || i === totalPages || (i >= this.currentPage - 2 && i <= this.currentPage + 2)) {
                paginationHTML += `
                    <li class="page-item ${i === this.currentPage ? 'active' : ''}">
                        <a class="page-link" href="#" onclick="employeeManager.goToPage(${i})">${i}</a>
                    </li>
                `;
            } else if (i === this.currentPage - 3 || i === this.currentPage + 3) {
                paginationHTML += '<li class="page-item disabled"><span class="page-link">...</span></li>';
            }
        }

        // Next button
        paginationHTML += `
            <li class="page-item ${this.currentPage === totalPages ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="employeeManager.goToPage(${this.currentPage + 1})">التالي</a>
            </li>
        `;

        pagination.innerHTML = paginationHTML;
    }

    updateTableFooter() {
        const totalEmployees = this.filteredEmployees.length;
        const activeEmployees = this.filteredEmployees.filter(emp => emp.status === 'active').length;
        const totalSalaries = this.filteredEmployees.reduce((sum, emp) => sum + parseFloat(emp.salary || 0), 0);
        const averageSalary = totalEmployees > 0 ? totalSalaries / totalEmployees : 0;

        // Update footer elements
        const totalElement = document.getElementById('totalEmployees');
        const activeElement = document.getElementById('activeEmployees');
        const salariesElement = document.getElementById('totalSalaries');
        const averageElement = document.getElementById('averageSalary');

        if (totalElement) totalElement.textContent = totalEmployees;
        if (activeElement) activeElement.textContent = activeEmployees;
        if (salariesElement) salariesElement.textContent = this.formatCurrency(totalSalaries);
        if (averageElement) averageElement.textContent = this.formatCurrency(averageSalary);
    }

    goToPage(page) {
        const totalPages = Math.ceil(this.filteredEmployees.length / this.itemsPerPage);
        if (page >= 1 && page <= totalPages) {
            this.currentPage = page;
            this.renderEmployees();
        }
    }

    refreshData() {
        this.loadData().then(() => {
            this.renderEmployees();
            this.showAlert('تم تحديث البيانات بنجاح', 'success');
        }).catch(error => {
            console.error('Error refreshing data:', error);
            this.showAlert('حدث خطأ في تحديث البيانات', 'danger');
        });
    }

    initializeDocumentManager() {
        // Initialize document manager when the modal is opened
        const documentModal = document.getElementById('documentModal');
        if (documentModal) {
            documentModal.addEventListener('shown.bs.modal', () => {
                if (!window.documentManager) {
                    // Create new instance
                    window.documentManager = new DocumentManager();
                    window.documentManager.init();
                } else {
                    // Refresh existing instance
                    window.documentManager.loadDocuments().then(() => {
                        window.documentManager.applyFilters();
                    });
                }
            });
        }
    }

    selectAllEmployees(checked) {
        const checkboxes = document.querySelectorAll('.employee-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = checked;
        });
    }

    showEmployeeModal(employee = null) {
        const modal = new bootstrap.Modal(document.getElementById('employeeModal'));
        const form = document.getElementById('employeeForm');
        const title = document.getElementById('employeeModalTitle');

        // Reset form
        form.reset();
        form.classList.remove('was-validated');

        if (employee) {
            // Edit mode
            title.textContent = 'تعديل بيانات الموظف';
            this.editingEmployeeId = employee.id;
            this.populateForm(employee);
        } else {
            // Add mode
            title.textContent = 'إضافة موظف جديد';
            this.editingEmployeeId = null;
            // Set default values
            document.getElementById('hireDate').value = new Date().toISOString().split('T')[0];
            document.getElementById('employeeStatus').value = 'active';
            document.getElementById('contractType').value = 'permanent';
        }

        modal.show();
    }

    populateForm(employee) {
        document.getElementById('employeeId').value = employee.id;
        document.getElementById('employeeName').value = employee.name || '';
        document.getElementById('employeeNumber').value = employee.employeeNumber || '';
        document.getElementById('nationalId').value = employee.nationalId || '';
        document.getElementById('phone').value = employee.phone || '';
        document.getElementById('email').value = employee.email || '';
        document.getElementById('birthDate').value = employee.birthDate || '';
        document.getElementById('department').value = employee.department || '';
        document.getElementById('position').value = employee.position || '';
        document.getElementById('hireDate').value = employee.hireDate || '';
        document.getElementById('salary').value = employee.salary || '';
        document.getElementById('employeeStatus').value = employee.status || '';
        document.getElementById('contractType').value = employee.contractType || '';
        document.getElementById('address').value = employee.address || '';
        document.getElementById('notes').value = employee.notes || '';

        // Filter positions based on selected department
        if (employee.department) {
            this.filterPositions(employee.department);
        }
    }

    filterPositions(department) {
        const positionSelect = document.getElementById('position');
        const currentValue = positionSelect.value;
        
        positionSelect.innerHTML = '<option value="">اختر الوظيفة</option>';
        
        const filteredPositions = this.positions.filter(pos => 
            !department || pos.department === department
        );
        
        filteredPositions.forEach(pos => {
            positionSelect.innerHTML += `<option value="${pos.title}">${pos.title}</option>`;
        });

        // Restore selected value if it's still valid
        if (currentValue && filteredPositions.some(pos => pos.title === currentValue)) {
            positionSelect.value = currentValue;
        }
    }

    // Utility functions
    formatCurrency(amount) {
        return new Intl.NumberFormat('ar-SA', {
            style: 'currency',
            currency: 'omr',
            minimumFractionDigits: 0
        }).format(amount || 0);
    }

    formatDate(dateString) {
        if (!dateString) return '-';
        const date = new Date(dateString);
        return date.toLocaleDateString('ar-SA');
    }

    getStatusClass(status) {
        switch (status) {
            case 'active': return 'status-active';
            case 'inactive': return 'status-inactive';
            case 'suspended': return 'status-suspended';
            default: return 'status-inactive';
        }
    }

    getStatusText(status) {
        switch (status) {
            case 'active': return 'نشط';
            case 'inactive': return 'غير نشط';
            case 'suspended': return 'معلق';
            default: return 'غير محدد';
        }
    }

    async saveEmployee(e) {
        e.preventDefault();

        const form = e.target;
        if (!form.checkValidity()) {
            e.stopPropagation();
            form.classList.add('was-validated');
            return;
        }

        const employeeData = {
            name: document.getElementById('employeeName').value,
            employeeNumber: document.getElementById('employeeNumber').value,
            nationalId: document.getElementById('nationalId').value,
            phone: document.getElementById('phone').value,
            email: document.getElementById('email').value,
            birthDate: document.getElementById('birthDate').value,
            department: document.getElementById('department').value,
            position: document.getElementById('position').value,
            hireDate: document.getElementById('hireDate').value,
            salary: parseFloat(document.getElementById('salary').value),
            status: document.getElementById('employeeStatus').value,
            contractType: document.getElementById('contractType').value,
            address: document.getElementById('address').value,
            notes: document.getElementById('notes').value,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };

        try {
            // Check for duplicate employee number
            const existingEmployee = this.employees.find(emp =>
                emp.employeeNumber === employeeData.employeeNumber &&
                emp.id !== this.editingEmployeeId
            );

            if (existingEmployee) {
                this.showAlert('رقم الموظف موجود مسبقاً', 'danger');
                return;
            }

            if (this.editingEmployeeId) {
                // Update existing employee
                employeeData.id = this.editingEmployeeId;
                employeeData.createdAt = this.employees.find(emp => emp.id === this.editingEmployeeId).createdAt;
                await dbManager.update('employees', employeeData);
                this.showAlert('تم تحديث بيانات الموظف بنجاح', 'success');
            } else {
                // Add new employee
                await dbManager.add('employees', employeeData);
                this.showAlert('تم إضافة الموظف بنجاح', 'success');
            }

            // Refresh data and close modal
            await this.loadData();
            this.renderEmployees();
            bootstrap.Modal.getInstance(document.getElementById('employeeModal')).hide();

        } catch (error) {
            console.error('Error saving employee:', error);
            this.showAlert('حدث خطأ في حفظ البيانات', 'danger');
        }
    }

    editEmployee(id) {
        const employee = this.employees.find(emp => emp.id === id);
        if (employee) {
            this.showEmployeeModal(employee);
        }
    }

    viewEmployee(id) {
        const employee = this.employees.find(emp => emp.id === id);
        if (employee) {
            // Create view modal content
            const viewContent = `
                <div class="row">
                    <div class="col-md-6">
                        <h6>المعلومات الشخصية</h6>
                        <p><strong>الاسم:</strong> ${employee.name}</p>
                        <p><strong>رقم الهوية:</strong> ${employee.nationalId || '-'}</p>
                        <p><strong>الهاتف:</strong> ${employee.phone || '-'}</p>
                        <p><strong>البريد الإلكتروني:</strong> ${employee.email || '-'}</p>
                        <p><strong>تاريخ الميلاد:</strong> ${this.formatDate(employee.birthDate)}</p>
                    </div>
                    <div class="col-md-6">
                        <h6>المعلومات الوظيفية</h6>
                        <p><strong>الرقم الوظيفي:</strong> ${employee.employeeNumber}</p>
                        <p><strong>القسم:</strong> ${employee.department}</p>
                        <p><strong>الوظيفة:</strong> ${employee.position}</p>
                        <p><strong>تاريخ التعيين:</strong> ${this.formatDate(employee.hireDate)}</p>
                        <p><strong>الراتب:</strong> ${this.formatCurrency(employee.salary)}</p>
                        <p><strong>الحالة:</strong> ${this.getStatusText(employee.status)}</p>
                    </div>
                </div>
                ${employee.address ? `<p><strong>العنوان:</strong> ${employee.address}</p>` : ''}
                ${employee.notes ? `<p><strong>ملاحظات:</strong> ${employee.notes}</p>` : ''}
            `;

            // Show in a modal or alert
            const viewModal = document.createElement('div');
            viewModal.className = 'modal fade';
            viewModal.innerHTML = `
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">بيانات الموظف: ${employee.name}</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            ${viewContent}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(viewModal);
            const modal = new bootstrap.Modal(viewModal);
            modal.show();

            // Remove modal from DOM when hidden
            viewModal.addEventListener('hidden.bs.modal', () => {
                viewModal.remove();
            });
        }
    }

    confirmDeleteEmployee(id) {
        const employee = this.employees.find(emp => emp.id === id);
        if (employee) {
            document.getElementById('deleteEmployeeName').textContent = employee.name;
            this.deletingEmployeeId = id;
            const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
            modal.show();
        }
    }

    async deleteEmployee() {
        if (!this.deletingEmployeeId) return;

        try {
            await dbManager.delete('employees', this.deletingEmployeeId);
            this.showAlert('تم حذف الموظف بنجاح', 'success');

            // Refresh data
            await this.loadData();
            this.renderEmployees();

            // Close modal
            bootstrap.Modal.getInstance(document.getElementById('deleteModal')).hide();

        } catch (error) {
            console.error('Error deleting employee:', error);
            this.showAlert('حدث خطأ في حذف الموظف', 'danger');
        }
    }

    async exportEmployees() {
        try {
            const { jsPDF } = window.jspdf;
            const doc = new jsPDF();

            // Add Arabic font support (you might need to include Arabic font)
            doc.setFont('helvetica');
            doc.setFontSize(16);
            doc.text('Employee Report', 20, 20);

            // Add table headers
            let yPosition = 40;
            const headers = ['ID', 'Name', 'Department', 'Position', 'Salary', 'Status'];

            doc.setFontSize(12);
            headers.forEach((header, index) => {
                doc.text(header, 20 + (index * 30), yPosition);
            });

            // Add employee data
            yPosition += 10;
            this.filteredEmployees.forEach((employee) => {
                if (yPosition > 280) {
                    doc.addPage();
                    yPosition = 20;
                }

                const row = [
                    employee.employeeNumber,
                    employee.name,
                    employee.department,
                    employee.position,
                    employee.salary.toString(),
                    this.getStatusText(employee.status)
                ];

                row.forEach((cell, cellIndex) => {
                    doc.text(cell.substring(0, 15), 20 + (cellIndex * 30), yPosition);
                });

                yPosition += 10;
            });

            // Save the PDF
            doc.save('employees-report.pdf');
            this.showAlert('تم تصدير التقرير بنجاح', 'success');

        } catch (error) {
            console.error('Error exporting employees:', error);
            this.showAlert('حدث خطأ في تصدير التقرير', 'danger');
        }
    }

    async exportToExcel() {
        try {
            // Create CSV content
            let csvContent = '';
            const headers = ['الرقم الوظيفي', 'الاسم', 'القسم', 'الوظيفة', 'الراتب', 'تاريخ التعيين', 'الحالة'];

            // Add headers
            csvContent += headers.join(',') + '\n';

            // Add employee data
            this.filteredEmployees.forEach(employee => {
                const row = [
                    employee.employeeNumber,
                    `"${employee.name}"`,
                    `"${employee.department}"`,
                    `"${employee.position}"`,
                    employee.salary,
                    employee.hireDate,
                    this.getStatusText(employee.status)
                ];
                csvContent += row.join(',') + '\n';
            });

            // Create and download file
            const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', 'employees-report.csv');
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            this.showAlert('تم تصدير التقرير بصيغة CSV بنجاح', 'success');

        } catch (error) {
            console.error('Error exporting to Excel:', error);
            this.showAlert('حدث خطأ في تصدير التقرير', 'danger');
        }
    }

    printEmployees() {
        try {
            if (this.filteredEmployees.length === 0) {
                this.showAlert('لا توجد بيانات موظفين للطباعة', 'warning');
                return;
            }

            // Calculate statistics
            const totalEmployees = this.filteredEmployees.length;
            const activeEmployees = this.filteredEmployees.filter(emp => emp.status === 'active').length;
            const totalSalaries = this.filteredEmployees.reduce((sum, emp) => sum + parseFloat(emp.salary || 0), 0);
            const averageSalary = totalEmployees > 0 ? totalSalaries / totalEmployees : 0;

            // Create print content
            const printContent = `
                <html>
                <head>
                    <title>تقرير الموظفين</title>
                    <style>
                        body {
                            font-family: Arial, sans-serif;
                            direction: rtl;
                            margin: 20px;
                            font-size: 12px;
                        }
                        .header {
                            text-align: center;
                            margin-bottom: 30px;
                            border-bottom: 2px solid #333;
                            padding-bottom: 20px;
                        }
                        .header h1 {
                            color: #333;
                            margin: 0;
                            font-size: 24px;
                        }
                        .header p {
                            margin: 5px 0;
                            color: #666;
                            font-size: 14px;
                        }
                        .summary {
                            background: #f8f9fa;
                            padding: 20px;
                            border-radius: 8px;
                            margin-bottom: 30px;
                            border: 1px solid #dee2e6;
                        }
                        .summary h3 {
                            margin-top: 0;
                            color: #333;
                            text-align: center;
                        }
                        .summary-grid {
                            display: grid;
                            grid-template-columns: repeat(2, 1fr);
                            gap: 15px;
                        }
                        .summary-item {
                            display: flex;
                            justify-content: space-between;
                            padding: 8px 0;
                            border-bottom: 1px solid #dee2e6;
                        }
                        table {
                            width: 100%;
                            border-collapse: collapse;
                            margin: 20px 0;
                            font-size: 11px;
                        }
                        th, td {
                            border: 1px solid #ddd;
                            padding: 8px;
                            text-align: center;
                        }
                        th {
                            background-color: #f8f9fa;
                            font-weight: bold;
                            color: #333;
                        }
                        .status-active { color: #28a745; font-weight: bold; }
                        .status-inactive { color: #dc3545; font-weight: bold; }
                        .status-suspended { color: #ffc107; font-weight: bold; }
                        .footer {
                            margin-top: 40px;
                            text-align: center;
                            font-size: 10px;
                            color: #666;
                            border-top: 1px solid #dee2e6;
                            padding-top: 20px;
                        }
                        @media print {
                            body { margin: 0; }
                            .no-print { display: none; }
                        }
                    </style>
                </head>
                <body>
                    <div class="header">
                        <h1>تقرير الموظفين</h1>
                        <p>تاريخ التقرير: ${new Date().toLocaleDateString('ar-SA')}</p>
                        <p>وقت الإنشاء: ${new Date().toLocaleTimeString('ar-SA')}</p>
                    </div>

                    <div class="summary">
                        <h3>ملخص إحصائي</h3>
                        <div class="summary-grid">
                            <div class="summary-item">
                                <span>إجمالي الموظفين:</span>
                                <span><strong>${totalEmployees}</strong></span>
                            </div>
                            <div class="summary-item">
                                <span>الموظفين النشطين:</span>
                                <span><strong>${activeEmployees}</strong></span>
                            </div>
                            <div class="summary-item">
                                <span>إجمالي الرواتب:</span>
                                <span><strong>${this.formatCurrency(totalSalaries)}</strong></span>
                            </div>
                            <div class="summary-item">
                                <span>متوسط الراتب:</span>
                                <span><strong>${this.formatCurrency(averageSalary)}</strong></span>
                            </div>
                        </div>
                    </div>

                    <table>
                        <thead>
                            <tr>
                                <th>الرقم الوظيفي</th>
                                <th>الاسم</th>
                                <th>القسم</th>
                                <th>الوظيفة</th>
                                <th>الراتب</th>
                                <th>تاريخ التعيين</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${this.filteredEmployees.map(employee => `
                                <tr>
                                    <td>${employee.employeeNumber}</td>
                                    <td>${employee.name}</td>
                                    <td>${employee.department}</td>
                                    <td>${employee.position}</td>
                                    <td>${this.formatCurrency(employee.salary)}</td>
                                    <td>${this.formatDate(employee.hireDate)}</td>
                                    <td class="status-${employee.status}">${this.getStatusText(employee.status)}</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>

                    <div class="footer">
                        <p>تم إنشاء هذا التقرير بواسطة نظام إدارة الموظفين</p>
                        <p>جميع الحقوق محفوظة © ${new Date().getFullYear()}</p>
                    </div>
                </body>
                </html>
            `;

            // Open print window
            const printWindow = window.open('', '_blank');
            printWindow.document.write(printContent);
            printWindow.document.close();
            printWindow.print();

            this.showAlert('تم فتح نافذة الطباعة', 'success');

        } catch (error) {
            console.error('Error printing employees:', error);
            this.showAlert('حدث خطأ في الطباعة', 'danger');
        }
    }

    showAlert(message, type = 'info') {
        // Create alert element
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        alertDiv.style.cssText = 'top: 20px; left: 20px; z-index: 9999; min-width: 300px;';
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(alertDiv);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 5000);
    }
}

// Global instance
const employeeManager = new EmployeeManager();

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', async function() {
    await initializeDatabase();
    await employeeManager.init();
});
