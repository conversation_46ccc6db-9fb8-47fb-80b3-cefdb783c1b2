/* Global Styles */
* {
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
    margin: 0;
    padding: 0;
}

/* Login Page Styles */
.login-page {
    height: 100vh;
    overflow: hidden;
}

.login-form-container {
    max-width: 400px;
    width: 100%;
    padding: 2rem;
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.features-container {
    background: linear-gradient(135deg, #007bff, #0056b3);
}

.feature-item {
    text-align: center;
    padding: 1rem;
    border-radius: 10px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

.demo-accounts {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 8px;
    border: 1px solid #dee2e6;
}

/* Dashboard Styles */
.sidebar {
    background: linear-gradient(180deg, #2c3e50, #34495e);
    min-height: 100vh;
    position: fixed;
    top: 0;
    right: 0;
    width: 250px;
    z-index: 1000;
    transition: all 0.3s ease;
}

.sidebar.collapsed {
    width: 70px;
}

.sidebar-header {
    padding: 1.5rem 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-menu {
    list-style: none;
    padding: 0;
    margin: 0;
}

.sidebar-menu li {
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.sidebar-menu a {
    display: flex;
    align-items: center;
    padding: 1rem;
    color: #ecf0f1;
    text-decoration: none;
    transition: all 0.3s ease;
}

.sidebar-menu a:hover,
.sidebar-menu a.active {
    background: rgba(255, 255, 255, 0.1);
    color: #3498db;
}

.sidebar-menu i {
    width: 20px;
    margin-left: 10px;
    text-align: center;
}

.main-content {
    margin-right: 250px;
    transition: all 0.3s ease;
}

.main-content.expanded {
    margin-right: 70px;
}

/* Header Styles */
.header {
    background: white;
    padding: 1rem 2rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-title {
    margin: 0;
    color: #2c3e50;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #3498db;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
}

/* Content Area */
.content-area {
    padding: 2rem;
}

/* Cards */
.stat-card {
    background: white;
    border-radius: 10px;
    padding: 1.5rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    border-left: 4px solid #3498db;
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-card.success {
    border-left-color: #27ae60;
}

.stat-card.warning {
    border-left-color: #f39c12;
}

.stat-card.danger {
    border-left-color: #e74c3c;
}

.stat-card.info {
    border-left-color: #3498db;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: bold;
    margin: 0;
}

.stat-label {
    color: #7f8c8d;
    margin: 0;
    font-size: 0.9rem;
}

/* Tables */
.table-container {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(52, 152, 219, 0.1);
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    min-height: 400px;
}

.table-container:hover {
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.table-header {
    background: linear-gradient(135deg, #3498db, #2980b9);
    padding: 1.5rem 2rem;
    border-bottom: none;
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: white;
    flex-shrink: 0;
}

.table-header h5 {
    margin: 0;
    font-weight: 600;
    font-size: 1.2rem;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.table-body {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.table-responsive {
    flex: 1;
    overflow-y: auto;
    max-height: none;
}

.table th {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-top: none;
    font-weight: 600;
    color: #2c3e50;
    position: sticky;
    top: 0;
    z-index: 10;
    text-align: center;
    padding: 1rem 0.75rem;
    border-bottom: 2px solid #3498db;
}

.table tbody tr {
    transition: all 0.3s ease;
}

.table tbody tr:hover {
    background: linear-gradient(135deg, #f8f9fa, #ffffff);
    transform: scale(1.01);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.table td {
    text-align: center;
    vertical-align: middle;
    padding: 0.75rem;
    border-bottom: 1px solid rgba(52, 152, 219, 0.1);
}

.table-footer {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    padding: 1rem 2rem;
    border-top: 2px solid #3498db;
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: #6c757d;
    font-size: 0.9rem;
    flex-shrink: 0;
    min-height: 60px;
}

.table-footer .summary-info {
    display: flex;
    gap: 2rem;
    align-items: center;
}

.table-footer .summary-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0.5rem 1rem;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 8px;
    min-width: 80px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.table-footer .summary-item .label {
    font-size: 0.75rem;
    color: #6c757d;
    margin-bottom: 0.2rem;
    font-weight: 500;
}

.table-footer .summary-item .value {
    font-weight: 700;
    color: #2c3e50;
    font-size: 1rem;
}

.table-footer .table-actions {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.table-footer .btn {
    padding: 0.4rem 0.8rem;
    font-size: 0.8rem;
    border-radius: 6px;
}

/* Forms */
.form-container {
    background: white;
    border-radius: 10px;
    padding: 2rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
}

.form-section {
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid #dee2e6;
}

.form-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.form-section h5 {
    color: #2c3e50;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #3498db;
    display: inline-block;
}

/* Buttons */
.btn-custom {
    border-radius: 8px;
    padding: 0.5rem 1.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-custom:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* Search and Filter */
.search-filter-container {
    background: white;
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
}

/* Modal Styles */
.modal-content {
    border-radius: 15px;
    border: none;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.modal-header {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    border-radius: 15px 15px 0 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(100%);
        width: 100%;
    }
    
    .sidebar.show {
        transform: translateX(0);
    }
    
    .main-content {
        margin-right: 0;
    }
    
    .header {
        padding: 1rem;
    }
    
    .content-area {
        padding: 1rem;
    }
    
    .stat-card {
        margin-bottom: 1rem;
    }
}

/* Loading Spinner */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Status Badges */
.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.status-active {
    background: #d4edda;
    color: #155724;
}

.status-inactive {
    background: #f8d7da;
    color: #721c24;
}

.status-suspended {
    background: #fff3cd;
    color: #856404;
}

/* Print Styles */
@media print {
    .sidebar,
    .header,
    .no-print {
        display: none !important;
    }
    
    .main-content {
        margin-right: 0;
    }
    
    .content-area {
        padding: 0;
    }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Document Management Styles */
.document-manager {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
}

.document-upload-area {
    border: 2px dashed #3498db;
    border-radius: 10px;
    padding: 3rem 2rem;
    text-align: center;
    background: linear-gradient(135deg, #f8f9fa, #ffffff);
    transition: all 0.3s ease;
    cursor: pointer;
}

.document-upload-area:hover {
    border-color: #2980b9;
    background: linear-gradient(135deg, #e3f2fd, #f8f9fa);
    transform: translateY(-2px);
}

.document-upload-area.dragover {
    border-color: #27ae60;
    background: linear-gradient(135deg, #e8f5e8, #f0f8f0);
}

.document-upload-icon {
    font-size: 3rem;
    color: #3498db;
    margin-bottom: 1rem;
    animation: pulse 2s infinite;
}

.document-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-top: 2rem;
}

.document-item {
    background: linear-gradient(135deg, #ffffff, #f8f9fa);
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    border: 1px solid rgba(52, 152, 219, 0.1);
}

.document-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.document-icon {
    width: 50px;
    height: 50px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    margin-bottom: 1rem;
}

.document-icon.pdf {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
}

.document-icon.excel {
    background: linear-gradient(135deg, #27ae60, #229954);
}

.document-icon.word {
    background: linear-gradient(135deg, #3498db, #2980b9);
}

.document-icon.image {
    background: linear-gradient(135deg, #f39c12, #e67e22);
}

.document-icon.default {
    background: linear-gradient(135deg, #95a5a6, #7f8c8d);
}

.document-info h6 {
    color: #2c3e50;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.document-meta {
    font-size: 0.85rem;
    color: #6c757d;
    margin-bottom: 1rem;
}

.document-actions {
    display: flex;
    gap: 0.5rem;
    justify-content: flex-end;
}

.document-actions .btn {
    padding: 0.4rem 0.8rem;
    font-size: 0.8rem;
    border-radius: 6px;
}

/* Document Categories */
.document-categories {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
}

.category-filter {
    padding: 0.5rem 1rem;
    border: 2px solid #3498db;
    background: transparent;
    color: #3498db;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.category-filter:hover,
.category-filter.active {
    background: #3498db;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
}

/* Document Search */
.document-search {
    position: relative;
    margin-bottom: 2rem;
}

.document-search input {
    padding-right: 3rem;
    border-radius: 25px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.document-search input:focus {
    border-color: #3498db;
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

.document-search .search-icon {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
}

/* Document Preview Modal */
.document-preview {
    max-width: 90vw;
    max-height: 90vh;
}

.document-preview iframe,
.document-preview img {
    width: 100%;
    height: 70vh;
    border: none;
    border-radius: 8px;
}

/* Chart containers - Fixed height controls */
.chart-container {
    position: relative;
    height: 300px !important;
    max-height: 300px !important;
    margin: 20px 0;
    overflow: hidden;
}

.chart-container canvas {
    max-height: 300px !important;
    height: 300px !important;
    width: 100% !important;
}

/* Dashboard specific chart containers */
.stat-card canvas {
    max-height: 350px !important;
    height: 350px !important;
}

/* Specific chart height controls */
#departmentChart,
#statusChart,
#employeeChart,
#payrollChart,
#departmentDistChart,
#salaryDistChart,
#payrollTrendChart,
#departmentCostChart,
#employeeDistChart {
    max-height: 300px !important;
    height: 300px !important;
}

/* Reports charts container - FIXED HEIGHT LIMIT */
#reportCharts {
    max-height: 900px !important;
    overflow: hidden;
}

#reportCharts .stat-card {
    max-height: 400px !important;
    overflow: hidden;
}

#reportCharts canvas {
    max-height: 350px !important;
    height: 350px !important;
}

/* Force all Chart.js canvases to respect height limits */
canvas[id*="Chart"] {
    max-height: 350px !important;
    height: 350px !important;
}

/* Container for charts in reports */
.row#reportCharts {
    max-height: 900px !important;
    overflow-y: auto;
}

.row#reportCharts .col-lg-6,
.row#reportCharts .col-lg-8,
.row#reportCharts .col-lg-4 {
    max-height: 450px !important;
    overflow: hidden;
}

/* Financial Management Styles */
.financial-tabs {
    border-bottom: 2px solid #dee2e6;
    margin-bottom: 0;
}

.financial-tabs .nav-link {
    border: none;
    border-bottom: 3px solid transparent;
    color: #6c757d;
    font-weight: 500;
    padding: 15px 20px;
    transition: all 0.3s ease;
}

.financial-tabs .nav-link:hover {
    border-color: #007bff;
    color: #007bff;
    background: rgba(0, 123, 255, 0.1);
}

.financial-tabs .nav-link.active {
    border-color: #007bff;
    color: #007bff;
    background: rgba(0, 123, 255, 0.1);
    font-weight: 600;
}

.financial-tab-content {
    padding: 0;
    background: white;
    border-radius: 0 0 10px 10px;
}

.financial-section {
    padding: 25px;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #f8f9fa;
}

.section-header h6 {
    margin: 0;
    color: #333;
    font-size: 18px;
    font-weight: 600;
}

.summary-stats {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    border: 1px solid #dee2e6;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #dee2e6;
}

.summary-item:last-child {
    border-bottom: none;
}

.summary-item .label {
    font-weight: 500;
    color: #6c757d;
}

.summary-item .value {
    font-weight: 600;
    font-size: 16px;
}

/* Financial Modal Enhancements */
.modal-header.bg-success,
.modal-header.bg-danger,
.modal-header.bg-warning {
    border-radius: 10px 10px 0 0;
}

.modal-content {
    border-radius: 10px;
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.modal-body {
    padding: 30px;
}

.form-label {
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
}

.form-control,
.form-select {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    padding: 12px 15px;
    transition: all 0.3s ease;
}

.form-control:focus,
.form-select:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Financial Status Badges */
.badge {
    font-size: 12px;
    padding: 6px 12px;
    border-radius: 20px;
    font-weight: 500;
}

/* Financial Chart Container */
#financialChart {
    max-height: 400px !important;
    height: 400px !important;
}

/* Financial Table Enhancements */
.financial-section .table {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.financial-section .table thead th {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    border: none;
    font-weight: 600;
    padding: 15px 12px;
    text-align: center;
}

.financial-section .table tbody td {
    padding: 12px;
    vertical-align: middle;
    border-color: #f8f9fa;
}

.financial-section .table tbody tr:hover {
    background: rgba(0, 123, 255, 0.05);
    transform: translateY(-1px);
    transition: all 0.3s ease;
}

/* Button Group Enhancements */
.btn-group-sm .btn {
    padding: 6px 10px;
    font-size: 12px;
    border-radius: 6px;
    margin: 0 2px;
}

.btn-group-sm .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Responsive Financial Management */
@media (max-width: 768px) {
    .financial-tabs .nav-link {
        padding: 10px 15px;
        font-size: 14px;
    }

    .section-header {
        flex-direction: column;
        gap: 15px;
        align-items: flex-start;
    }

    .summary-stats {
        margin-top: 20px;
    }

    .financial-section {
        padding: 15px;
    }

    .modal-body {
        padding: 20px;
    }
}

/* Leave Management Styles */
.leave-tabs {
    border-bottom: 2px solid #dee2e6;
    margin-bottom: 0;
}

.leave-tabs .nav-link {
    border: none;
    border-bottom: 3px solid transparent;
    color: #6c757d;
    font-weight: 500;
    padding: 15px 20px;
    transition: all 0.3s ease;
}

.leave-tabs .nav-link:hover {
    border-color: #28a745;
    color: #28a745;
    background: rgba(40, 167, 69, 0.1);
}

.leave-tabs .nav-link.active {
    border-color: #28a745;
    color: #28a745;
    background: rgba(40, 167, 69, 0.1);
    font-weight: 600;
}

.leave-tab-content {
    padding: 0;
    background: white;
    border-radius: 0 0 10px 10px;
}

.leave-section {
    padding: 25px;
}

/* Leave Calendar Styles */
.calendar-container {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.calendar-header {
    text-align: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #f8f9fa;
}

.calendar-header h5 {
    margin: 0;
    color: #333;
    font-weight: 600;
}

.calendar-grid {
    display: grid;
    gap: 10px;
}

.calendar-weekdays {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 5px;
    margin-bottom: 10px;
}

.weekday {
    text-align: center;
    font-weight: 600;
    color: #6c757d;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 5px;
}

.calendar-days {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 5px;
}

.calendar-day {
    position: relative;
    min-height: 60px;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    padding: 8px;
    background: white;
    cursor: pointer;
    transition: all 0.3s ease;
}

.calendar-day:hover {
    background: #f8f9fa;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.calendar-day.empty {
    border: none;
    background: transparent;
    cursor: default;
}

.calendar-day.has-leave {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    border-color: #28a745;
}

.calendar-day.has-leave:hover {
    background: linear-gradient(135deg, #218838, #1ea080);
}

.day-number {
    font-weight: 600;
    font-size: 14px;
}

.leave-count {
    position: absolute;
    top: 5px;
    left: 5px;
    background: rgba(255, 255, 255, 0.9);
    color: #333;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    font-weight: bold;
}

.calendar-legend {
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid #dee2e6;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 5px;
}

.legend-color {
    width: 20px;
    height: 20px;
    border-radius: 3px;
    border: 1px solid #dee2e6;
}

.legend-color.has-leave {
    background: linear-gradient(135deg, #28a745, #20c997);
}

/* Leave Status Badges */
.badge.bg-success {
    background: linear-gradient(135deg, #28a745, #20c997) !important;
}

.badge.bg-warning {
    background: linear-gradient(135deg, #ffc107, #fd7e14) !important;
}

.badge.bg-danger {
    background: linear-gradient(135deg, #dc3545, #e83e8c) !important;
}

.badge.bg-info {
    background: linear-gradient(135deg, #17a2b8, #6f42c1) !important;
}

/* Leave Table Enhancements */
.leave-section .table {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.leave-section .table thead th {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    border: none;
    font-weight: 600;
    padding: 15px 12px;
    text-align: center;
}

.leave-section .table tbody td {
    padding: 12px;
    vertical-align: middle;
    border-color: #f8f9fa;
}

.leave-section .table tbody tr:hover {
    background: rgba(40, 167, 69, 0.05);
    transform: translateY(-1px);
    transition: all 0.3s ease;
}

/* Leave Modal Enhancements */
.modal-header.bg-success {
    background: linear-gradient(135deg, #28a745, #20c997) !important;
    border-radius: 10px 10px 0 0;
}

.modal-header.bg-primary {
    background: linear-gradient(135deg, #007bff, #6610f2) !important;
    border-radius: 10px 10px 0 0;
}

/* Leave Balance Progress */
.balance-progress {
    position: relative;
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
    margin-top: 5px;
}

.balance-progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #28a745, #20c997);
    transition: width 0.3s ease;
}

.balance-progress-bar.warning {
    background: linear-gradient(90deg, #ffc107, #fd7e14);
}

.balance-progress-bar.danger {
    background: linear-gradient(90deg, #dc3545, #e83e8c);
}

/* Responsive Leave Management */
@media (max-width: 768px) {
    .leave-tabs .nav-link {
        padding: 10px 15px;
        font-size: 14px;
    }

    .calendar-days {
        grid-template-columns: repeat(7, 1fr);
        gap: 2px;
    }

    .calendar-day {
        min-height: 40px;
        padding: 4px;
    }

    .weekday {
        padding: 5px;
        font-size: 12px;
    }

    .leave-section {
        padding: 15px;
    }

    .section-header {
        flex-direction: column;
        gap: 15px;
        align-items: flex-start;
    }
}
