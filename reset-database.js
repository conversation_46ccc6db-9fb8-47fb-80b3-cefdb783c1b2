// Database Reset and Initialization Script
class DatabaseReset {
    constructor() {
        this.isResetting = false;
    }

    // Main reset function
    async resetDatabase() {
        if (this.isResetting) {
            console.log('Database reset already in progress...');
            return;
        }

        this.isResetting = true;
        console.log('🔄 Starting database reset...');

        try {
            // Clear all existing data
            this.clearAllData();
            
            // Create fresh sample data
            await this.createSampleEmployees();
            await this.createSamplePayroll();
            await this.createSampleFinancialData();
            await this.createSampleLeaves();
            await this.createSampleAttendance();
            await this.createSystemSettings();

            console.log('✅ Database reset completed successfully!');
            this.showSuccessMessage();
            
            // Refresh current page
            setTimeout(() => {
                window.location.reload();
            }, 2000);

        } catch (error) {
            console.error('❌ Error during database reset:', error);
            this.showErrorMessage(error.message);
        } finally {
            this.isResetting = false;
        }
    }

    // Clear all localStorage data
    clearAllData() {
        console.log('🗑️ Clearing all existing data...');
        
        const keysToRemove = [
            'employees',
            'payroll',
            'allowances',
            'penalties', 
            'loans',
            'leaves',
            'leaveBalances',
            'attendance',
            'settings',
            'reports',
            'documents'
        ];

        keysToRemove.forEach(key => {
            localStorage.removeItem(key);
        });

        console.log('✅ All data cleared');
    }

    // Create sample employees
    async createSampleEmployees() {
        console.log('👥 Creating sample employees...');

        const employees = [
            {
                id: '1',
                name: 'أحمد محمد علي السالم',
                employeeNumber: 'EMP001',
                department: 'تقنية المعلومات',
                position: 'مطور برمجيات أول',
                salary: 12000,
                email: '<EMAIL>',
                phone: '**********',
                hireDate: '2022-01-15',
                birthDate: '1990-05-20',
                nationalId: '**********',
                status: 'active',
                address: 'الرياض، حي النرجس',
                emergencyContact: 'محمد علي - **********',
                bankAccount: 'SA1234567**********89',
                maritalStatus: 'متزوج',
                education: 'بكالوريوس علوم حاسب',
                experience: '5 سنوات'
            },
            {
                id: '2',
                name: 'فاطمة أحمد السالم',
                employeeNumber: 'EMP002',
                department: 'الموارد البشرية',
                position: 'مدير الموارد البشرية',
                salary: 10000,
                email: '<EMAIL>',
                phone: '**********',
                hireDate: '2022-02-01',
                birthDate: '1988-08-15',
                nationalId: '**********',
                status: 'active',
                address: 'الرياض، حي العليا',
                emergencyContact: 'أحمد السالم - **********',
                bankAccount: 'SA234567**********890',
                maritalStatus: 'متزوجة',
                education: 'ماجستير إدارة أعمال',
                experience: '8 سنوات'
            },
            {
                id: '3',
                name: 'محمد عبدالله الأحمد',
                employeeNumber: 'EMP003',
                department: 'المالية',
                position: 'محاسب أول',
                salary: 9000,
                email: '<EMAIL>',
                phone: '**********',
                hireDate: '2022-03-10',
                birthDate: '1985-12-03',
                nationalId: '**********',
                status: 'active',
                address: 'الرياض، حي الملز',
                emergencyContact: 'عبدالله الأحمد - **********',
                bankAccount: 'SA34567**********8901',
                maritalStatus: 'متزوج',
                education: 'بكالوريوس محاسبة',
                experience: '10 سنوات'
            },
            {
                id: '4',
                name: 'نورا سعد المطيري',
                employeeNumber: 'EMP004',
                department: 'التسويق',
                position: 'أخصائي تسويق رقمي',
                salary: 7500,
                email: '<EMAIL>',
                phone: '**********',
                hireDate: '2022-04-05',
                birthDate: '1992-03-25',
                nationalId: '**********',
                status: 'active',
                address: 'الرياض، حي الياسمين',
                emergencyContact: 'سعد المطيري - **********',
                bankAccount: 'SA4567**********89012',
                maritalStatus: 'عزباء',
                education: 'بكالوريوس تسويق',
                experience: '3 سنوات'
            },
            {
                id: '5',
                name: 'خالد عبدالرحمن القحطاني',
                employeeNumber: 'EMP005',
                department: 'المبيعات',
                position: 'مدير مبيعات',
                salary: 11000,
                email: '<EMAIL>',
                phone: '**********',
                hireDate: '2022-05-20',
                birthDate: '1987-07-10',
                nationalId: '**********',
                status: 'active',
                address: 'الرياض، حي الروضة',
                emergencyContact: 'عبدالرحمن القحطاني - **********',
                bankAccount: 'SA567**********890123',
                maritalStatus: 'متزوج',
                education: 'بكالوريوس إدارة أعمال',
                experience: '7 سنوات'
            },
            {
                id: '6',
                name: 'سارة محمد الزهراني',
                employeeNumber: 'EMP006',
                department: 'خدمة العملاء',
                position: 'مشرف خدمة العملاء',
                salary: 6500,
                email: '<EMAIL>',
                phone: '**********',
                hireDate: '2022-06-15',
                birthDate: '1993-11-18',
                nationalId: '**********',
                status: 'active',
                address: 'الرياض، حي الفيصلية',
                emergencyContact: 'محمد الزهراني - **********',
                bankAccount: 'SA67**********8901234',
                maritalStatus: 'متزوجة',
                education: 'دبلوم إدارة أعمال',
                experience: '4 سنوات'
            },
            {
                id: '7',
                name: 'عبدالعزيز سالم الغامدي',
                employeeNumber: 'EMP007',
                department: 'الأمن والسلامة',
                position: 'مسؤول أمن وسلامة',
                salary: 5500,
                email: '<EMAIL>',
                phone: '**********',
                hireDate: '2022-07-01',
                birthDate: '1989-04-12',
                nationalId: '**********',
                status: 'active',
                address: 'الرياض، حي الشفا',
                emergencyContact: 'سالم الغامدي - **********',
                bankAccount: '*********************',
                maritalStatus: 'متزوج',
                education: 'دبلوم أمن وسلامة',
                experience: '6 سنوات'
            },
            {
                id: '8',
                name: 'ريم عبدالله الدوسري',
                employeeNumber: 'EMP008',
                department: 'الشؤون الإدارية',
                position: 'منسق إداري',
                salary: 6000,
                email: '<EMAIL>',
                phone: '**********',
                hireDate: '2022-08-10',
                birthDate: '1991-09-05',
                nationalId: '**********',
                status: 'active',
                address: 'الرياض، حي الورود',
                emergencyContact: 'عبدالله الدوسري - **********',
                bankAccount: 'SA**********890123456',
                maritalStatus: 'عزباء',
                education: 'بكالوريوس إدارة عامة',
                experience: '2 سنة'
            }
        ];

        localStorage.setItem('employees', JSON.stringify(employees));
        console.log(`✅ Created ${employees.length} sample employees`);
    }

    // Create sample payroll data
    async createSamplePayroll() {
        console.log('💰 Creating sample payroll data...');

        const currentDate = new Date();
        const currentMonth = currentDate.getMonth() + 1;
        const currentYear = currentDate.getFullYear();

        const payrollRecords = [
            {
                id: '1',
                employeeId: '1',
                month: currentMonth,
                year: currentYear,
                basicSalary: 12000,
                allowances: 2000,
                overtime: 500,
                deductions: 300,
                netSalary: 14200,
                status: 'paid',
                payDate: new Date().toISOString(),
                createdAt: new Date().toISOString()
            },
            {
                id: '2',
                employeeId: '2',
                month: currentMonth,
                year: currentYear,
                basicSalary: 10000,
                allowances: 1500,
                overtime: 0,
                deductions: 200,
                netSalary: 11300,
                status: 'paid',
                payDate: new Date().toISOString(),
                createdAt: new Date().toISOString()
            },
            {
                id: '3',
                employeeId: '3',
                month: currentMonth,
                year: currentYear,
                basicSalary: 9000,
                allowances: 1000,
                overtime: 300,
                deductions: 150,
                netSalary: 10150,
                status: 'pending',
                payDate: null,
                createdAt: new Date().toISOString()
            }
        ];

        localStorage.setItem('payroll', JSON.stringify(payrollRecords));
        console.log(`✅ Created ${payrollRecords.length} payroll records`);
    }

    // Create sample financial data
    async createSampleFinancialData() {
        console.log('🏦 Creating sample financial data...');

        // Allowances
        const allowances = [
            {
                id: '1',
                employeeId: '1',
                type: 'performance',
                amount: 1000,
                date: '2024-01-15',
                frequency: 'monthly',
                status: 'active',
                notes: 'علاوة أداء متميز',
                createdAt: new Date().toISOString()
            },
            {
                id: '2',
                employeeId: '2',
                type: 'transportation',
                amount: 500,
                date: '2024-01-01',
                frequency: 'monthly',
                status: 'active',
                notes: 'بدل مواصلات',
                createdAt: new Date().toISOString()
            },
            {
                id: '3',
                employeeId: '5',
                type: 'commission',
                amount: 2000,
                date: '2024-01-20',
                frequency: 'one-time',
                status: 'active',
                notes: 'عمولة مبيعات الربع الأول',
                createdAt: new Date().toISOString()
            }
        ];

        // Penalties
        const penalties = [
            {
                id: '1',
                employeeId: '4',
                type: 'late',
                amount: 200,
                date: '2024-01-10',
                deductionType: 'immediate',
                installments: 1,
                reason: 'تأخير متكرر عن العمل',
                status: 'active',
                createdAt: new Date().toISOString()
            },
            {
                id: '2',
                employeeId: '6',
                type: 'absence',
                amount: 500,
                date: '2024-01-05',
                deductionType: 'installments',
                installments: 2,
                reason: 'غياب بدون عذر',
                status: 'active',
                createdAt: new Date().toISOString()
            }
        ];

        // Loans
        const loans = [
            {
                id: '1',
                employeeId: '3',
                type: 'personal',
                amount: 15000,
                date: '2024-01-01',
                duration: 12,
                interestRate: 0,
                monthlyInstallment: 1250,
                paidAmount: 2500,
                purpose: 'احتياجات شخصية',
                notes: 'قرض بدون فوائد',
                status: 'active',
                createdAt: new Date().toISOString()
            },
            {
                id: '2',
                employeeId: '7',
                type: 'emergency',
                amount: 8000,
                date: '2024-01-15',
                duration: 8,
                interestRate: 0,
                monthlyInstallment: 1000,
                paidAmount: 1000,
                purpose: 'ظروف طارئة',
                notes: 'قرض طوارئ',
                status: 'active',
                createdAt: new Date().toISOString()
            }
        ];

        localStorage.setItem('allowances', JSON.stringify(allowances));
        localStorage.setItem('penalties', JSON.stringify(penalties));
        localStorage.setItem('loans', JSON.stringify(loans));

        console.log(`✅ Created financial data: ${allowances.length} allowances, ${penalties.length} penalties, ${loans.length} loans`);
    }

    // Create sample leaves data
    async createSampleLeaves() {
        console.log('🏖️ Creating sample leaves data...');

        const leaves = [
            {
                id: '1',
                employeeId: '1',
                type: 'annual',
                startDate: '2024-02-01',
                endDate: '2024-02-05',
                days: 5,
                reason: 'إجازة سنوية مع العائلة',
                notes: 'سفر خارج المملكة',
                status: 'approved',
                withPay: true,
                requestDate: '2024-01-15T10:00:00.000Z',
                approvedBy: 'مدير الموارد البشرية',
                approvedDate: '2024-01-16T14:30:00.000Z'
            },
            {
                id: '2',
                employeeId: '2',
                type: 'sick',
                startDate: '2024-01-20',
                endDate: '2024-01-22',
                days: 3,
                reason: 'إجازة مرضية - نزلة برد',
                notes: 'تقرير طبي مرفق',
                status: 'approved',
                withPay: true,
                requestDate: '2024-01-19T08:00:00.000Z',
                approvedBy: 'مدير الموارد البشرية',
                approvedDate: '2024-01-19T10:00:00.000Z'
            },
            {
                id: '3',
                employeeId: '4',
                type: 'emergency',
                startDate: '2024-02-10',
                endDate: '2024-02-12',
                days: 3,
                reason: 'ظروف عائلية طارئة',
                notes: 'وفاة أحد الأقارب',
                status: 'pending',
                withPay: true,
                requestDate: '2024-02-08T16:00:00.000Z',
                approvedBy: null,
                approvedDate: null
            },
            {
                id: '4',
                employeeId: '5',
                type: 'annual',
                startDate: '2024-03-01',
                endDate: '2024-03-07',
                days: 7,
                reason: 'إجازة سنوية - شهر العسل',
                notes: 'حجز طيران مؤكد',
                status: 'pending',
                withPay: true,
                requestDate: '2024-02-01T12:00:00.000Z',
                approvedBy: null,
                approvedDate: null
            }
        ];

        // Leave balances
        const leaveBalances = [
            {
                employeeId: '1',
                annualLeave: { total: 30, used: 5, remaining: 25 },
                sickLeave: { total: 15, used: 0, remaining: 15 },
                lastUpdated: new Date().toISOString()
            },
            {
                employeeId: '2',
                annualLeave: { total: 30, used: 0, remaining: 30 },
                sickLeave: { total: 15, used: 3, remaining: 12 },
                lastUpdated: new Date().toISOString()
            },
            {
                employeeId: '3',
                annualLeave: { total: 30, used: 0, remaining: 30 },
                sickLeave: { total: 15, used: 0, remaining: 15 },
                lastUpdated: new Date().toISOString()
            },
            {
                employeeId: '4',
                annualLeave: { total: 30, used: 0, remaining: 30 },
                sickLeave: { total: 15, used: 0, remaining: 15 },
                lastUpdated: new Date().toISOString()
            },
            {
                employeeId: '5',
                annualLeave: { total: 30, used: 0, remaining: 30 },
                sickLeave: { total: 15, used: 0, remaining: 15 },
                lastUpdated: new Date().toISOString()
            },
            {
                employeeId: '6',
                annualLeave: { total: 30, used: 0, remaining: 30 },
                sickLeave: { total: 15, used: 0, remaining: 15 },
                lastUpdated: new Date().toISOString()
            },
            {
                employeeId: '7',
                annualLeave: { total: 30, used: 0, remaining: 30 },
                sickLeave: { total: 15, used: 0, remaining: 15 },
                lastUpdated: new Date().toISOString()
            },
            {
                employeeId: '8',
                annualLeave: { total: 30, used: 0, remaining: 30 },
                sickLeave: { total: 15, used: 0, remaining: 15 },
                lastUpdated: new Date().toISOString()
            }
        ];

        localStorage.setItem('leaves', JSON.stringify(leaves));
        localStorage.setItem('leaveBalances', JSON.stringify(leaveBalances));

        console.log(`✅ Created ${leaves.length} leave records and ${leaveBalances.length} leave balances`);
    }

    // Create sample attendance data
    async createSampleAttendance() {
        console.log('⏰ Creating sample attendance data...');

        const attendance = [];
        const today = new Date();

        // Create attendance for last 7 days
        for (let i = 6; i >= 0; i--) {
            const date = new Date(today);
            date.setDate(date.getDate() - i);
            const dateStr = date.toISOString().split('T')[0];

            // Skip weekends (Friday and Saturday)
            if (date.getDay() === 5 || date.getDay() === 6) continue;

            // Create attendance for each employee
            for (let empId = 1; empId <= 8; empId++) {
                const checkIn = new Date(date);
                checkIn.setHours(8 + Math.floor(Math.random() * 2), Math.floor(Math.random() * 60));

                const checkOut = new Date(checkIn);
                checkOut.setHours(checkIn.getHours() + 8 + Math.floor(Math.random() * 2), Math.floor(Math.random() * 60));

                const workHours = (checkOut - checkIn) / (1000 * 60 * 60);
                const overtime = Math.max(0, workHours - 8);

                attendance.push({
                    id: `${empId}_${dateStr}`,
                    employeeId: empId.toString(),
                    date: dateStr,
                    checkIn: checkIn.toISOString(),
                    checkOut: checkOut.toISOString(),
                    workHours: parseFloat(workHours.toFixed(2)),
                    overtime: parseFloat(overtime.toFixed(2)),
                    status: workHours >= 8 ? 'present' : 'partial',
                    notes: ''
                });
            }
        }

        localStorage.setItem('attendance', JSON.stringify(attendance));
        console.log(`✅ Created ${attendance.length} attendance records`);
    }

    // Create system settings
    async createSystemSettings() {
        console.log('⚙️ Creating system settings...');

        const settings = {
            company: {
                name: 'شركة التقنية المتقدمة',
                nameEn: 'Advanced Technology Company',
                logo: '',
                address: 'الرياض، المملكة العربية السعودية',
                phone: '+966 11 123 4567',
                email: '<EMAIL>',
                website: 'www.advtech.com.sa',
                taxNumber: '**********12345',
                commercialRegister: '**********'
            },
            payroll: {
                workingDaysPerMonth: 22,
                workingHoursPerDay: 8,
                overtimeRate: 1.5,
                socialInsuranceRate: 9.75,
                medicalInsuranceRate: 2.5,
                paymentDay: 25,
                currency: 'SAR'
            },
            leaves: {
                annualLeaveDefault: 30,
                sickLeaveDefault: 15,
                maternityLeave: 70,
                paternityLeave: 3,
                carryOverLimit: 5,
                maxConsecutiveDays: 30
            },
            attendance: {
                workStartTime: '08:00',
                workEndTime: '17:00',
                breakDuration: 60,
                lateThreshold: 15,
                earlyLeaveThreshold: 15,
                weekends: ['friday', 'saturday']
            },
            notifications: {
                emailEnabled: true,
                smsEnabled: false,
                leaveApprovalNotification: true,
                payrollNotification: true,
                attendanceAlerts: true
            },
            system: {
                language: 'ar',
                timezone: 'Asia/Riyadh',
                dateFormat: 'DD/MM/YYYY',
                timeFormat: '24h',
                backupFrequency: 'daily',
                sessionTimeout: 30
            }
        };

        localStorage.setItem('settings', JSON.stringify(settings));
        console.log('✅ Created system settings');
    }

    // Show success message
    showSuccessMessage() {
        const message = `
            <div class="alert alert-success alert-dismissible fade show position-fixed" style="top: 20px; left: 50%; transform: translateX(-50%); z-index: 9999; min-width: 400px;">
                <h5><i class="fas fa-check-circle me-2"></i>تم إعادة تهيئة قاعدة البيانات بنجاح!</h5>
                <hr>
                <p class="mb-1"><strong>تم إنشاء:</strong></p>
                <ul class="mb-2">
                    <li>8 موظفين نموذجيين</li>
                    <li>بيانات رواتب شاملة</li>
                    <li>علاوات وغرامات وقروض</li>
                    <li>طلبات إجازات وأرصدة</li>
                    <li>سجلات حضور وانصراف</li>
                    <li>إعدادات النظام</li>
                </ul>
                <p class="mb-0"><small>سيتم إعادة تحميل الصفحة خلال ثانيتين...</small></p>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        document.body.insertAdjacentHTML('beforeend', message);
    }

    // Show error message
    showErrorMessage(error) {
        const message = `
            <div class="alert alert-danger alert-dismissible fade show position-fixed" style="top: 20px; left: 50%; transform: translateX(-50%); z-index: 9999; min-width: 400px;">
                <h5><i class="fas fa-exclamation-triangle me-2"></i>خطأ في إعادة تهيئة قاعدة البيانات</h5>
                <p class="mb-0">${error}</p>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        document.body.insertAdjacentHTML('beforeend', message);
    }

    // Quick reset function for testing
    quickReset() {
        if (confirm('هل أنت متأكد من إعادة تهيئة قاعدة البيانات؟\n\nسيتم حذف جميع البيانات الحالية وإنشاء بيانات تجريبية جديدة.')) {
            this.resetDatabase();
        }
    }
}

// Create global instance
const dbReset = new DatabaseReset();

// Make it globally available
window.resetDatabase = () => dbReset.quickReset();
window.dbReset = dbReset;

// Auto-initialize if needed
document.addEventListener('DOMContentLoaded', function() {
    // Check if database is empty and offer to initialize
    const employees = localStorage.getItem('employees');
    if (!employees || JSON.parse(employees).length === 0) {
        console.log('🔍 No employee data found. Database may need initialization.');

        // Show initialization prompt after a short delay
        setTimeout(() => {
            if (confirm('لم يتم العثور على بيانات في النظام.\n\nهل تريد إنشاء بيانات تجريبية للبدء؟')) {
                dbReset.resetDatabase();
            }
        }, 1000);
    }
});

console.log('🔧 Database reset utility loaded. Use resetDatabase() to reset all data.');
