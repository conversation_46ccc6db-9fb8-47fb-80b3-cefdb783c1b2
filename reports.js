// Reports Management
class ReportsManager {
    constructor() {
        this.employees = [];
        this.departments = [];
        this.payrollData = [];
        this.currentReportData = null;
        this.currentCharts = {};
    }

    async init() {
        await this.loadData();
        this.setupEventListeners();
        this.populateDropdowns();
        this.setupSidebar();
    }

    async loadData() {
        try {
            this.employees = await dbManager.getAll('employees');
            this.departments = await dbManager.getAll('departments');
            this.payrollData = await dbManager.getAll('payroll');
        } catch (error) {
            console.error('Error loading data:', error);
            this.showAlert('حدث خطأ في تحميل البيانات', 'danger');
        }
    }

    populateDropdowns() {
        // Populate department filter
        const departmentFilter = document.getElementById('departmentFilter');
        departmentFilter.innerHTML = '<option value="">جميع الأقسام</option>';
        
        this.departments.forEach(dept => {
            departmentFilter.innerHTML += `<option value="${dept.name}">${dept.name}</option>`;
        });
    }

    setupEventListeners() {
        // Report period change
        document.getElementById('reportPeriod').addEventListener('change', (e) => {
            const customDateRange = document.getElementById('customDateRange');
            const customDateRangeTo = document.getElementById('customDateRangeTo');
            
            if (e.target.value === 'custom') {
                customDateRange.style.display = 'block';
                customDateRangeTo.style.display = 'block';
            } else {
                customDateRange.style.display = 'none';
                customDateRangeTo.style.display = 'none';
            }
        });

        // Generate report button
        document.getElementById('generateReportBtn').addEventListener('click', () => this.generateReport());

        // Export buttons
        document.getElementById('exportPdfBtn').addEventListener('click', () => this.exportToPDF());
        document.getElementById('exportExcelBtn').addEventListener('click', () => this.exportToExcel());
    }

    setupSidebar() {
        const sidebarToggle = document.getElementById('sidebarToggle');
        const sidebar = document.getElementById('sidebar');
        const mainContent = document.getElementById('mainContent');

        if (sidebarToggle) {
            sidebarToggle.addEventListener('click', () => {
                sidebar.classList.toggle('collapsed');
                mainContent.classList.toggle('expanded');
            });
        }
    }

    async generateReport() {
        const reportType = document.getElementById('reportType').value;
        const reportPeriod = document.getElementById('reportPeriod').value;
        const departmentFilter = document.getElementById('departmentFilter').value;

        try {
            let reportData;
            
            switch (reportType) {
                case 'employees':
                    reportData = await this.generateEmployeesReport(departmentFilter);
                    break;
                case 'payroll':
                    reportData = await this.generatePayrollReport(reportPeriod, departmentFilter);
                    break;
                case 'departments':
                    reportData = await this.generateDepartmentsReport();
                    break;
                case 'attendance':
                    reportData = await this.generateAttendanceReport(reportPeriod, departmentFilter);
                    break;
                default:
                    this.showAlert('نوع التقرير غير مدعوم', 'warning');
                    return;
            }

            this.currentReportData = reportData;
            this.displayReport(reportData);
            
        } catch (error) {
            console.error('Error generating report:', error);
            this.showAlert('حدث خطأ في إنشاء التقرير', 'danger');
        }
    }

    async generateEmployeesReport(departmentFilter) {
        let filteredEmployees = [...this.employees];
        
        if (departmentFilter) {
            filteredEmployees = filteredEmployees.filter(emp => emp.department === departmentFilter);
        }

        const stats = {
            totalEmployees: filteredEmployees.length,
            activeEmployees: filteredEmployees.filter(emp => emp.status === 'active').length,
            inactiveEmployees: filteredEmployees.filter(emp => emp.status === 'inactive').length,
            suspendedEmployees: filteredEmployees.filter(emp => emp.status === 'suspended').length
        };

        const departmentDistribution = {};
        this.departments.forEach(dept => {
            departmentDistribution[dept.name] = filteredEmployees.filter(emp => emp.department === dept.name).length;
        });

        return {
            type: 'employees',
            title: 'تقرير الموظفين',
            subtitle: departmentFilter ? `قسم ${departmentFilter}` : 'جميع الأقسام',
            stats,
            data: filteredEmployees,
            charts: {
                departmentDistribution,
                statusDistribution: {
                    'نشط': stats.activeEmployees,
                    'غير نشط': stats.inactiveEmployees,
                    'معلق': stats.suspendedEmployees
                }
            }
        };
    }

    async generatePayrollReport(period, departmentFilter) {
        const currentDate = new Date();
        let startDate, endDate;

        switch (period) {
            case 'current':
                startDate = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
                endDate = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0);
                break;
            case 'last':
                startDate = new Date(currentDate.getFullYear(), currentDate.getMonth() - 1, 1);
                endDate = new Date(currentDate.getFullYear(), currentDate.getMonth(), 0);
                break;
            case 'quarter':
                const quarter = Math.floor(currentDate.getMonth() / 3);
                startDate = new Date(currentDate.getFullYear(), quarter * 3, 1);
                endDate = new Date(currentDate.getFullYear(), (quarter + 1) * 3, 0);
                break;
            case 'year':
                startDate = new Date(currentDate.getFullYear(), 0, 1);
                endDate = new Date(currentDate.getFullYear(), 11, 31);
                break;
            case 'custom':
                startDate = new Date(document.getElementById('dateFrom').value);
                endDate = new Date(document.getElementById('dateTo').value);
                break;
            default:
                startDate = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
                endDate = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0);
        }

        let filteredPayroll = this.payrollData.filter(payroll => {
            const payrollDate = new Date(payroll.year, payroll.month - 1);
            return payrollDate >= startDate && payrollDate <= endDate;
        });

        if (departmentFilter) {
            filteredPayroll = filteredPayroll.filter(payroll => {
                const employee = this.employees.find(emp => emp.id === payroll.employeeId);
                return employee && employee.department === departmentFilter;
            });
        }

        const stats = {
            totalRecords: filteredPayroll.length,
            totalBasicSalary: filteredPayroll.reduce((sum, p) => sum + p.basicSalary, 0),
            totalDeductions: filteredPayroll.reduce((sum, p) => sum + p.totalDeductions, 0),
            totalNetSalary: filteredPayroll.reduce((sum, p) => sum + p.netSalary, 0)
        };

        return {
            type: 'payroll',
            title: 'تقرير الرواتب',
            subtitle: this.getPeriodText(period),
            stats,
            data: filteredPayroll,
            charts: {
                monthlyTrend: this.calculateMonthlyPayrollTrend(filteredPayroll),
                departmentCosts: this.calculateDepartmentCosts(filteredPayroll)
            }
        };
    }

    async generateDepartmentsReport() {
        const departmentStats = this.departments.map(dept => {
            const deptEmployees = this.employees.filter(emp => emp.department === dept.name);
            const activeEmployees = deptEmployees.filter(emp => emp.status === 'active');
            
            const totalSalaries = activeEmployees.reduce((sum, emp) => sum + (emp.salary || 0), 0);
            const avgSalary = activeEmployees.length > 0 ? totalSalaries / activeEmployees.length : 0;

            return {
                name: dept.name,
                description: dept.description,
                totalEmployees: deptEmployees.length,
                activeEmployees: activeEmployees.length,
                inactiveEmployees: deptEmployees.filter(emp => emp.status !== 'active').length,
                totalSalaries,
                avgSalary
            };
        });

        const stats = {
            totalDepartments: this.departments.length,
            totalEmployees: this.employees.length,
            avgEmployeesPerDept: this.employees.length / this.departments.length,
            totalSalaryBudget: departmentStats.reduce((sum, dept) => sum + dept.totalSalaries, 0)
        };

        return {
            type: 'departments',
            title: 'تقرير الأقسام',
            subtitle: 'إحصائيات شاملة للأقسام',
            stats,
            data: departmentStats,
            charts: {
                employeeDistribution: departmentStats.reduce((acc, dept) => {
                    acc[dept.name] = dept.totalEmployees;
                    return acc;
                }, {}),
                salaryDistribution: departmentStats.reduce((acc, dept) => {
                    acc[dept.name] = dept.totalSalaries;
                    return acc;
                }, {})
            }
        };
    }

    async generateAttendanceReport(period, departmentFilter) {
        // This would require attendance data - for now, return mock data
        const stats = {
            totalRecords: 0,
            presentDays: 0,
            absentDays: 0,
            lateDays: 0
        };

        return {
            type: 'attendance',
            title: 'تقرير الحضور والانصراف',
            subtitle: this.getPeriodText(period),
            stats,
            data: [],
            charts: {
                attendanceTrend: {},
                departmentAttendance: {}
            }
        };
    }

    displayReport(reportData) {
        // Update report header
        document.getElementById('reportTitle').textContent = reportData.title;
        document.getElementById('reportSubtitle').textContent = reportData.subtitle;

        // Display statistics
        this.displayReportStats(reportData.stats, reportData.type);

        // Display charts
        this.displayReportCharts(reportData.charts, reportData.type);

        // Display data table
        this.displayReportTable(reportData.data, reportData.type);

        // Show report content
        document.getElementById('reportContent').style.display = 'block';
    }

    displayReportStats(stats, reportType) {
        const statsContainer = document.getElementById('reportStats');
        let statsHTML = '';

        switch (reportType) {
            case 'employees':
                statsHTML = `
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-card info">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h3 class="stat-number">${stats.totalEmployees}</h3>
                                    <p class="stat-label">إجمالي الموظفين</p>
                                </div>
                                <i class="fas fa-users fa-2x text-info"></i>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-card success">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h3 class="stat-number">${stats.activeEmployees}</h3>
                                    <p class="stat-label">الموظفين النشطين</p>
                                </div>
                                <i class="fas fa-user-check fa-2x text-success"></i>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-card warning">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h3 class="stat-number">${stats.inactiveEmployees}</h3>
                                    <p class="stat-label">غير النشطين</p>
                                </div>
                                <i class="fas fa-user-times fa-2x text-warning"></i>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-card danger">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h3 class="stat-number">${stats.suspendedEmployees}</h3>
                                    <p class="stat-label">المعلقين</p>
                                </div>
                                <i class="fas fa-user-slash fa-2x text-danger"></i>
                            </div>
                        </div>
                    </div>
                `;
                break;
            case 'payroll':
                statsHTML = `
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-card info">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h3 class="stat-number">${stats.totalRecords}</h3>
                                    <p class="stat-label">عدد السجلات</p>
                                </div>
                                <i class="fas fa-file-alt fa-2x text-info"></i>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-card success">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h3 class="stat-number">${this.formatCurrency(stats.totalBasicSalary)}</h3>
                                    <p class="stat-label">إجمالي الراتب الأساسي</p>
                                </div>
                                <i class="fas fa-money-bill fa-2x text-success"></i>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-card warning">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h3 class="stat-number">${this.formatCurrency(stats.totalDeductions)}</h3>
                                    <p class="stat-label">إجمالي الخصومات</p>
                                </div>
                                <i class="fas fa-minus-circle fa-2x text-warning"></i>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-card danger">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h3 class="stat-number">${this.formatCurrency(stats.totalNetSalary)}</h3>
                                    <p class="stat-label">صافي الراتب</p>
                                </div>
                                <i class="fas fa-hand-holding-usd fa-2x text-danger"></i>
                            </div>
                        </div>
                    </div>
                `;
                break;
        }

        statsContainer.innerHTML = statsHTML;
    }

    // Utility functions
    formatCurrency(amount) {
        return new Intl.NumberFormat('ar-SA', {
            style: 'currency',
            currency: 'omr',
            minimumFractionDigits: 0
        }).format(amount || 0);
    }

    formatDate(dateString) {
        if (!dateString) return '-';
        const date = new Date(dateString);
        return date.toLocaleDateString('ar-SA');
    }

    getPeriodText(period) {
        switch (period) {
            case 'current': return 'الشهر الحالي';
            case 'last': return 'الشهر الماضي';
            case 'quarter': return 'الربع الحالي';
            case 'year': return 'السنة الحالية';
            case 'custom': return 'فترة مخصصة';
            default: return 'الشهر الحالي';
        }
    }

    displayReportCharts(charts, reportType) {
        const chartsContainer = document.getElementById('reportCharts');

        // Clear existing charts
        Object.values(this.currentCharts).forEach(chart => {
            if (chart && typeof chart.destroy === 'function') {
                chart.destroy();
            }
        });
        this.currentCharts = {};

        let chartsHTML = '';

        switch (reportType) {
            case 'employees':
                chartsHTML = `
                    <div class="col-lg-6 mb-3">
                        <div class="stat-card">
                            <h6 class="mb-3">توزيع الموظفين حسب القسم</h6>
                            <canvas id="departmentChart" height="200"></canvas>
                        </div>
                    </div>
                    <div class="col-lg-6 mb-3">
                        <div class="stat-card">
                            <h6 class="mb-3">توزيع الموظفين حسب الحالة</h6>
                            <canvas id="statusChart" height="200"></canvas>
                        </div>
                    </div>
                `;
                break;
            case 'payroll':
                chartsHTML = `
                    <div class="col-lg-8 mb-3">
                        <div class="stat-card">
                            <h6 class="mb-3">اتجاه الرواتب الشهرية</h6>
                            <canvas id="payrollTrendChart" height="150"></canvas>
                        </div>
                    </div>
                    <div class="col-lg-4 mb-3">
                        <div class="stat-card">
                            <h6 class="mb-3">تكاليف الأقسام</h6>
                            <canvas id="departmentCostChart" height="150"></canvas>
                        </div>
                    </div>
                `;
                break;
            case 'departments':
                chartsHTML = `
                    <div class="col-lg-6 mb-3">
                        <div class="stat-card">
                            <h6 class="mb-3">توزيع الموظفين</h6>
                            <canvas id="employeeDistChart" height="200"></canvas>
                        </div>
                    </div>
                    <div class="col-lg-6 mb-3">
                        <div class="stat-card">
                            <h6 class="mb-3">توزيع الرواتب</h6>
                            <canvas id="salaryDistChart" height="200"></canvas>
                        </div>
                    </div>
                `;
                break;
        }

        chartsContainer.innerHTML = chartsHTML;

        // Create charts after DOM update
        setTimeout(() => {
            this.createCharts(charts, reportType);
        }, 100);
    }

    createCharts(charts, reportType) {
        switch (reportType) {
            case 'employees':
                this.createDepartmentChart(charts.departmentDistribution);
                this.createStatusChart(charts.statusDistribution);
                break;
            case 'payroll':
                this.createPayrollTrendChart(charts.monthlyTrend);
                this.createDepartmentCostChart(charts.departmentCosts);
                break;
            case 'departments':
                this.createEmployeeDistChart(charts.employeeDistribution);
                this.createSalaryDistChart(charts.salaryDistribution);
                break;
        }
    }

    createDepartmentChart(data) {
        const ctx = document.getElementById('departmentChart');
        if (!ctx) return;

        this.currentCharts.departmentChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: Object.keys(data),
                datasets: [{
                    label: 'عدد الموظفين',
                    data: Object.values(data),
                    backgroundColor: '#3498db',
                    borderColor: '#2980b9',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                }
            }
        });
    }

    createStatusChart(data) {
        const ctx = document.getElementById('statusChart');
        if (!ctx) return;

        this.currentCharts.statusChart = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: Object.keys(data),
                datasets: [{
                    data: Object.values(data),
                    backgroundColor: ['#2ecc71', '#e74c3c', '#f39c12'],
                    borderColor: ['#27ae60', '#c0392b', '#e67e22'],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }

    displayReportTable(data, reportType) {
        const tableHead = document.getElementById('reportTableHead');
        const tableBody = document.getElementById('reportTableBody');
        const tableTitle = document.getElementById('tableTitle');

        let headers = [];
        let rows = [];

        switch (reportType) {
            case 'employees':
                tableTitle.textContent = 'قائمة الموظفين';
                headers = ['الرقم الوظيفي', 'الاسم', 'القسم', 'الوظيفة', 'الراتب', 'تاريخ التعيين', 'الحالة'];
                rows = data.map(emp => [
                    emp.employeeNumber,
                    emp.name,
                    emp.department,
                    emp.position,
                    this.formatCurrency(emp.salary),
                    this.formatDate(emp.hireDate),
                    this.getStatusText(emp.status)
                ]);
                break;
            case 'payroll':
                tableTitle.textContent = 'سجلات الرواتب';
                headers = ['الموظف', 'القسم', 'الشهر/السنة', 'الراتب الأساسي', 'الخصومات', 'صافي الراتب'];
                rows = data.map(payroll => {
                    const employee = this.employees.find(emp => emp.id === payroll.employeeId);
                    return [
                        employee ? employee.name : 'غير معروف',
                        payroll.department,
                        `${payroll.month}/${payroll.year}`,
                        this.formatCurrency(payroll.basicSalary),
                        this.formatCurrency(payroll.totalDeductions),
                        this.formatCurrency(payroll.netSalary)
                    ];
                });
                break;
            case 'departments':
                tableTitle.textContent = 'إحصائيات الأقسام';
                headers = ['القسم', 'إجمالي الموظفين', 'الموظفين النشطين', 'متوسط الراتب', 'إجمالي الرواتب'];
                rows = data.map(dept => [
                    dept.name,
                    dept.totalEmployees,
                    dept.activeEmployees,
                    this.formatCurrency(dept.avgSalary),
                    this.formatCurrency(dept.totalSalaries)
                ]);
                break;
        }

        // Create table headers
        tableHead.innerHTML = `
            <tr>
                ${headers.map(header => `<th>${header}</th>`).join('')}
            </tr>
        `;

        // Create table rows
        if (rows.length === 0) {
            tableBody.innerHTML = `<tr><td colspan="${headers.length}" class="text-center text-muted">لا توجد بيانات</td></tr>`;
        } else {
            tableBody.innerHTML = rows.map(row => `
                <tr>
                    ${row.map(cell => `<td>${cell}</td>`).join('')}
                </tr>
            `).join('');
        }
    }

    calculateMonthlyPayrollTrend(payrollData) {
        const monthlyData = {};
        payrollData.forEach(record => {
            const key = `${record.year}-${record.month.toString().padStart(2, '0')}`;
            if (!monthlyData[key]) {
                monthlyData[key] = 0;
            }
            monthlyData[key] += record.netSalary;
        });
        return monthlyData;
    }

    calculateDepartmentCosts(payrollData) {
        const departmentCosts = {};
        payrollData.forEach(record => {
            if (!departmentCosts[record.department]) {
                departmentCosts[record.department] = 0;
            }
            departmentCosts[record.department] += record.netSalary;
        });
        return departmentCosts;
    }

    getStatusText(status) {
        switch (status) {
            case 'active': return 'نشط';
            case 'inactive': return 'غير نشط';
            case 'suspended': return 'معلق';
            default: return 'غير محدد';
        }
    }

    async generateQuickReport(reportType) {
        switch (reportType) {
            case 'active-employees':
                document.getElementById('reportType').value = 'employees';
                document.getElementById('departmentFilter').value = '';
                break;
            case 'monthly-payroll':
                document.getElementById('reportType').value = 'payroll';
                document.getElementById('reportPeriod').value = 'current';
                break;
            case 'departments':
                document.getElementById('reportType').value = 'departments';
                break;
            case 'performance':
                this.showAlert('تقرير الأداء قيد التطوير', 'info');
                return;
        }

        await this.generateReport();
    }

    async exportToPDF() {
        if (!this.currentReportData) {
            this.showAlert('لا يوجد تقرير لتصديره', 'warning');
            return;
        }

        try {
            const { jsPDF } = window.jspdf;
            const doc = new jsPDF();

            // Add title
            doc.setFontSize(16);
            doc.text(this.currentReportData.title, 20, 20);
            doc.setFontSize(12);
            doc.text(this.currentReportData.subtitle, 20, 30);

            // Add generation date
            doc.setFontSize(10);
            doc.text(`Generated on: ${new Date().toLocaleDateString()}`, 20, 40);

            // Add data table
            let yPosition = 60;
            const data = this.currentReportData.data;

            if (data && data.length > 0) {
                // Add headers based on report type
                let headers = [];
                switch (this.currentReportData.type) {
                    case 'employees':
                        headers = ['ID', 'Name', 'Department', 'Position', 'Salary'];
                        break;
                    case 'payroll':
                        headers = ['Employee', 'Department', 'Month/Year', 'Basic', 'Net'];
                        break;
                    case 'departments':
                        headers = ['Department', 'Total Emp', 'Active', 'Avg Salary'];
                        break;
                }

                // Add headers
                doc.setFontSize(10);
                headers.forEach((header, index) => {
                    doc.text(header, 20 + (index * 35), yPosition);
                });

                yPosition += 10;

                // Add data rows
                data.slice(0, 30).forEach((item) => { // Limit to 30 rows
                    if (yPosition > 280) {
                        doc.addPage();
                        yPosition = 20;
                    }

                    let row = [];
                    switch (this.currentReportData.type) {
                        case 'employees':
                            row = [
                                item.employeeNumber,
                                item.name.substring(0, 15),
                                item.department.substring(0, 10),
                                item.position.substring(0, 10),
                                item.salary.toString()
                            ];
                            break;
                        case 'payroll':
                            const employee = this.employees.find(emp => emp.id === item.employeeId);
                            row = [
                                employee ? employee.name.substring(0, 15) : 'Unknown',
                                item.department.substring(0, 10),
                                `${item.month}/${item.year}`,
                                item.basicSalary.toFixed(0),
                                item.netSalary.toFixed(0)
                            ];
                            break;
                        case 'departments':
                            row = [
                                item.name.substring(0, 15),
                                item.totalEmployees.toString(),
                                item.activeEmployees.toString(),
                                item.avgSalary.toFixed(0)
                            ];
                            break;
                    }

                    row.forEach((cell, cellIndex) => {
                        doc.text(cell, 20 + (cellIndex * 35), yPosition);
                    });

                    yPosition += 8;
                });
            }

            // Save the PDF
            doc.save(`${this.currentReportData.type}-report.pdf`);
            this.showAlert('تم تصدير التقرير بنجاح', 'success');

        } catch (error) {
            console.error('Error exporting to PDF:', error);
            this.showAlert('حدث خطأ في تصدير التقرير', 'danger');
        }
    }

    async exportToExcel() {
        this.showAlert('تصدير Excel قيد التطوير', 'info');
    }

    showAlert(message, type = 'info') {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        alertDiv.style.cssText = 'top: 20px; left: 20px; z-index: 9999; min-width: 300px;';
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(alertDiv);

        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 5000);
    }
}

// Global instance
const reportsManager = new ReportsManager();

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', async function() {
    await initializeDatabase();
    await reportsManager.init();
});
