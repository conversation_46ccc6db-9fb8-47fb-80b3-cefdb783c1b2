// Database Management using IndexedDB
class DatabaseManager {
    constructor() {
        this.dbName = 'EmployeeManagementDB';
        this.dbVersion = 1;
        this.db = null;
    }

    // Initialize database
    async init() {
        return new Promise((resolve, reject) => {
            const request = indexedDB.open(this.dbName, this.dbVersion);

            request.onerror = () => {
                console.error('Database error:', request.error);
                reject(request.error);
            };

            request.onsuccess = () => {
                this.db = request.result;
                console.log('Database initialized successfully');
                resolve(this.db);
            };

            request.onupgradeneeded = (event) => {
                const db = event.target.result;
                this.createTables(db);
            };
        });
    }

    // Create database tables
    createTables(db) {
        // Users table
        if (!db.objectStoreNames.contains('users')) {
            const usersStore = db.createObjectStore('users', { keyPath: 'id', autoIncrement: true });
            usersStore.createIndex('username', 'username', { unique: true });
            usersStore.createIndex('role', 'role', { unique: false });
            
            // Add default users
            const defaultUsers = [
                { username: 'admin', password: 'admin123', role: 'admin', name: 'مدير النظام', active: true },
                { username: 'hr', password: 'hr123', role: 'hr', name: 'مشرف الموارد البشرية', active: true },
                { username: 'accountant', password: 'acc123', role: 'accountant', name: 'المحاسب', active: true }
            ];
            
            defaultUsers.forEach(user => {
                usersStore.add(user);
            });
        }

        // Employees table
        if (!db.objectStoreNames.contains('employees')) {
            const employeesStore = db.createObjectStore('employees', { keyPath: 'id', autoIncrement: true });
            employeesStore.createIndex('employeeNumber', 'employeeNumber', { unique: true });
            employeesStore.createIndex('department', 'department', { unique: false });
            employeesStore.createIndex('status', 'status', { unique: false });
            employeesStore.createIndex('name', 'name', { unique: false });

            // Add sample employees
            const sampleEmployees = [
                {
                    name: 'أحمد محمد العلي',
                    employeeNumber: 'EMP001',
                    nationalId: '**********',
                    phone: '**********',
                    email: '<EMAIL>',
                    birthDate: '1985-03-15',
                    department: 'الإدارة العامة',
                    position: 'مدير عام',
                    hireDate: '2020-01-01',
                    salary: 15000,
                    status: 'active',
                    contractType: 'permanent',
                    address: 'الرياض، حي النخيل',
                    notes: 'مدير عام الشركة',
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString()
                },
                {
                    name: 'فاطمة سعد الزهراني',
                    employeeNumber: 'EMP002',
                    nationalId: '2345678901',
                    phone: '0502345678',
                    email: '<EMAIL>',
                    birthDate: '1990-07-22',
                    department: 'الموارد البشرية',
                    position: 'مشرف موارد بشرية',
                    hireDate: '2021-03-15',
                    salary: 8000,
                    status: 'active',
                    contractType: 'permanent',
                    address: 'الرياض، حي الملز',
                    notes: 'مسؤولة عن التوظيف والتدريب',
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString()
                },
                {
                    name: 'خالد عبدالله المطيري',
                    employeeNumber: 'EMP003',
                    nationalId: '3456789012',
                    phone: '0503456789',
                    email: '<EMAIL>',
                    birthDate: '1988-11-10',
                    department: 'المحاسبة والمالية',
                    position: 'محاسب رئيسي',
                    hireDate: '2020-06-01',
                    salary: 9000,
                    status: 'active',
                    contractType: 'permanent',
                    address: 'الرياض، حي العليا',
                    notes: 'مسؤول عن الحسابات والتقارير المالية',
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString()
                },
                {
                    name: 'نورا أحمد القحطاني',
                    employeeNumber: 'EMP004',
                    nationalId: '4567890123',
                    phone: '0504567890',
                    email: '<EMAIL>',
                    birthDate: '1992-05-18',
                    department: 'تقنية المعلومات',
                    position: 'مطور برمجيات',
                    hireDate: '2022-01-10',
                    salary: 8500,
                    status: 'active',
                    contractType: 'permanent',
                    address: 'الرياض، حي الورود',
                    notes: 'مطورة تطبيقات ويب',
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString()
                },
                {
                    name: 'محمد سالم الغامدي',
                    employeeNumber: 'EMP005',
                    nationalId: '5678901234',
                    phone: '0505678901',
                    email: '<EMAIL>',
                    birthDate: '1987-09-25',
                    department: 'المبيعات والتسويق',
                    position: 'مشرف مبيعات',
                    hireDate: '2021-08-15',
                    salary: 7500,
                    status: 'active',
                    contractType: 'permanent',
                    address: 'الرياض، حي الصحافة',
                    notes: 'مسؤول عن فريق المبيعات',
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString()
                },
                {
                    name: 'سارة عبدالرحمن الدوسري',
                    employeeNumber: 'EMP006',
                    nationalId: '6789012345',
                    phone: '0506789012',
                    email: '<EMAIL>',
                    birthDate: '1993-12-08',
                    department: 'خدمة العملاء',
                    position: 'أخصائي خدمة عملاء',
                    hireDate: '2022-05-20',
                    salary: 4500,
                    status: 'active',
                    contractType: 'permanent',
                    address: 'الرياض، حي الربوة',
                    notes: 'متخصصة في خدمة العملاء',
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString()
                }
            ];

            sampleEmployees.forEach(employee => {
                employeesStore.add(employee);
            });
        }

        // Departments table
        if (!db.objectStoreNames.contains('departments')) {
            const departmentsStore = db.createObjectStore('departments', { keyPath: 'id', autoIncrement: true });
            departmentsStore.createIndex('name', 'name', { unique: true });
            
            // Add default departments
            const defaultDepartments = [
                { name: 'الإدارة العامة', description: 'الإدارة العليا والتخطيط الاستراتيجي' },
                { name: 'الموارد البشرية', description: 'إدارة شؤون الموظفين والتوظيف' },
                { name: 'المحاسبة والمالية', description: 'الشؤون المالية والمحاسبية' },
                { name: 'تقنية المعلومات', description: 'الأنظمة التقنية والدعم الفني' },
                { name: 'المبيعات والتسويق', description: 'المبيعات والأنشطة التسويقية' },
                { name: 'خدمة العملاء', description: 'دعم ومساعدة العملاء' }
            ];
            
            defaultDepartments.forEach(dept => {
                departmentsStore.add(dept);
            });
        }

        // Positions table
        if (!db.objectStoreNames.contains('positions')) {
            const positionsStore = db.createObjectStore('positions', { keyPath: 'id', autoIncrement: true });
            positionsStore.createIndex('title', 'title', { unique: false });
            positionsStore.createIndex('department', 'department', { unique: false });
            
            // Add default positions
            const defaultPositions = [
                { title: 'مدير عام', department: 'الإدارة العامة', baseSalary: 15000 },
                { title: 'مدير إدارة', department: 'الإدارة العامة', baseSalary: 12000 },
                { title: 'مشرف موارد بشرية', department: 'الموارد البشرية', baseSalary: 8000 },
                { title: 'أخصائي موارد بشرية', department: 'الموارد البشرية', baseSalary: 6000 },
                { title: 'محاسب رئيسي', department: 'المحاسبة والمالية', baseSalary: 9000 },
                { title: 'محاسب', department: 'المحاسبة والمالية', baseSalary: 6500 },
                { title: 'مطور برمجيات', department: 'تقنية المعلومات', baseSalary: 8500 },
                { title: 'مشرف مبيعات', department: 'المبيعات والتسويق', baseSalary: 7500 },
                { title: 'ممثل مبيعات', department: 'المبيعات والتسويق', baseSalary: 5500 },
                { title: 'أخصائي خدمة عملاء', department: 'خدمة العملاء', baseSalary: 4500 }
            ];
            
            defaultPositions.forEach(position => {
                positionsStore.add(position);
            });
        }

        // Payroll table
        if (!db.objectStoreNames.contains('payroll')) {
            const payrollStore = db.createObjectStore('payroll', { keyPath: 'id', autoIncrement: true });
            payrollStore.createIndex('employeeId', 'employeeId', { unique: false });
            payrollStore.createIndex('month', 'month', { unique: false });
            payrollStore.createIndex('year', 'year', { unique: false });
        }

        // Attendance table
        if (!db.objectStoreNames.contains('attendance')) {
            const attendanceStore = db.createObjectStore('attendance', { keyPath: 'id', autoIncrement: true });
            attendanceStore.createIndex('employeeId', 'employeeId', { unique: false });
            attendanceStore.createIndex('date', 'date', { unique: false });
        }

        // Settings table
        if (!db.objectStoreNames.contains('settings')) {
            const settingsStore = db.createObjectStore('settings', { keyPath: 'key' });
            
            // Add default settings
            const defaultSettings = [
                { key: 'workingDaysPerMonth', value: 22 },
                { key: 'workingHoursPerDay', value: 8 },
                { key: 'overtimeRate', value: 1.5 },
                { key: 'companyName', value: 'شركة النموذج المحدودة' },
                { key: 'companyAddress', value: 'الرياض، المملكة العربية السعودية' },
                { key: 'currency', value: 'ريال عماني' }
            ];
            
            defaultSettings.forEach(setting => {
                settingsStore.add(setting);
            });
        }
    }

    // Generic CRUD operations
    async add(storeName, data) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readwrite');
            const store = transaction.objectStore(storeName);
            const request = store.add(data);

            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    async get(storeName, id) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readonly');
            const store = transaction.objectStore(storeName);
            const request = store.get(id);

            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    async getAll(storeName) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readonly');
            const store = transaction.objectStore(storeName);
            const request = store.getAll();

            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    async update(storeName, data) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readwrite');
            const store = transaction.objectStore(storeName);
            const request = store.put(data);

            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    async delete(storeName, id) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readwrite');
            const store = transaction.objectStore(storeName);
            const request = store.delete(id);

            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    // Search by index
    async getByIndex(storeName, indexName, value) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readonly');
            const store = transaction.objectStore(storeName);
            const index = store.index(indexName);
            const request = index.get(value);

            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    async getAllByIndex(storeName, indexName, value) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readonly');
            const store = transaction.objectStore(storeName);
            const index = store.index(indexName);
            const request = index.getAll(value);

            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }
}

// Global database instance
const dbManager = new DatabaseManager();

// Initialize database function
async function initializeDatabase() {
    try {
        await dbManager.init();
        console.log('Database ready');
        return true;
    } catch (error) {
        console.error('Failed to initialize database:', error);
        return false;
    }
}
