# نظام إدارة الموظفين
## Employee Management System

نظام شامل لإدارة ومحاسبة الموظفين مصمم للشركات الصغيرة والمتوسطة (20-100 موظف).

## المزايا الرئيسية

### 🔐 نظام المصادقة
- تسجيل دخول آمن للمسؤولين
- ثلاثة مستويات صلاحيات: مدير النظام، موارد بشرية، محاسب
- إدارة جلسات المستخدمين

### 👥 إدارة الموظفين
- إضافة وتعديل وحذف بيانات الموظفين
- تخزين المعلومات الشخصية والوظيفية
- تتبع حالة الموظفين (نشط، غير نشط، معلق)
- البحث والتصفية المتقدمة

### 💰 حساب الرواتب
- حساب تلقائي للرواتب بناءً على:
  - عدد أيام العمل
  - الساعات الإضافية
  - البدلات والمكافآت
  - الخصومات والغياب
- إدارة كشوف الرواتب الشهرية
- حساب التأمين الاجتماعي

### 📊 التقارير والإحصائيات
- تقارير شاملة للموظفين والرواتب
- إحصائيات الأقسام
- رسوم بيانية تفاعلية
- تصدير التقارير بصيغة PDF

### ⏰ الحضور والانصراف
- تسجيل حضور وانصراف الموظفين
- تتبع الغياب والتأخير
- إحصائيات الحضور اليومية

### 📁 إدارة المستندات (جديد!)
- رفع المستندات بالسحب والإفلات
- تصنيف المستندات (عقود، شهادات، تقارير، شخصية)
- بحث متقدم في المستندات
- معاينة فورية للملفات (PDF، صور)
- تحميل وحذف المستندات
- دعم أنواع ملفات متعددة

### ⚙️ الإعدادات
- إعدادات الشركة
- إعدادات الرواتب والحسابات
- إدارة الأقسام والوظائف
- إدارة المستخدمين

## التقنيات المستخدمة

- **Frontend**: HTML5, CSS3, JavaScript (ES6+)
- **UI Framework**: Bootstrap 5
- **Database**: IndexedDB (قاعدة بيانات محلية)
- **Charts**: Chart.js
- **PDF Export**: jsPDF
- **Icons**: Font Awesome

## متطلبات التشغيل

- متصفح ويب حديث يدعم:
  - IndexedDB
  - ES6 JavaScript
  - CSS Grid/Flexbox
- لا يتطلب خادم أو قاعدة بيانات خارجية

## طريقة التشغيل

1. قم بتحميل جميع الملفات في مجلد واحد
2. افتح ملف `index.html` في المتصفح
3. استخدم أحد الحسابات التجريبية للدخول:

### الحسابات التجريبية

| النوع | اسم المستخدم | كلمة المرور |
|-------|-------------|-------------|
| مدير النظام | admin | admin123 |
| موارد بشرية | hr | hr123 |
| محاسب | accountant | acc123 |

## هيكل المشروع

```
├── index.html              # صفحة تسجيل الدخول
├── dashboard.html          # لوحة التحكم الرئيسية
├── employees.html          # إدارة الموظفين
├── payroll.html           # حساب الرواتب
├── reports.html           # التقارير
├── attendance.html        # الحضور والانصراف
├── settings.html          # الإعدادات
├── styles.css             # ملف التصميم الرئيسي
├── database.js            # إدارة قاعدة البيانات
├── auth.js               # نظام المصادقة
├── dashboard.js          # منطق لوحة التحكم
├── employees.js          # منطق إدارة الموظفين
├── payroll.js            # منطق حساب الرواتب
├── reports.js            # منطق التقارير
└── README.md             # ملف التوثيق
```

## الصلاحيات

### مدير النظام (Admin)
- جميع الصلاحيات
- إدارة المستخدمين
- إعدادات النظام
- حذف البيانات

### موارد بشرية (HR)
- إدارة الموظفين
- عرض التقارير
- تصدير البيانات

### محاسب (Accountant)
- حساب الرواتب
- التقارير المالية
- تصدير كشوف الرواتب

## المزايا التقنية

### قاعدة البيانات المحلية
- استخدام IndexedDB لتخزين البيانات محلياً
- لا يتطلب اتصال بالإنترنت
- أمان عالي للبيانات الحساسة

### تصميم متجاوب
- يعمل على جميع الأجهزة (كمبيوتر، تابلت، موبايل)
- واجهة مستخدم سهلة وبديهية
- دعم اللغة العربية بالكامل

### الأداء
- تحميل سريع
- معالجة البيانات محلياً
- لا يتطلب خادم

## الجديد في الإصدار 1.5.0 🎉

### 🎨 تحسينات التصميم الجديدة
- **خطوط عربية محسنة** من Google Fonts (Cairo & Tajawal)
- **تدرجات لونية احترافية** لجميع العناصر
- **انيميشن متقدم** للبطاقات والأزرار والنماذج
- **تأثيرات hover ديناميكية** للجداول والعناصر التفاعلية
- **شريط جانبي محسن** بتأثيرات بصرية متقدمة
- **بطاقات إحصائية محسنة** مع أشرطة تقدم وأيقونات متحركة
- **جداول محسنة مع تذييل احترافي** يعرض الإحصائيات المهمة

### 🚀 وظائف جديدة ومحسنة
- **تصدير Excel/CSV كامل** للموظفين والتقارير
- **وظيفة طباعة محسنة** مع تنسيق HTML احترافي
- **نظام تقارير متقدم** مع رسوم بيانية تفاعلية
- **إدارة كاملة للإعدادات** (أقسام، وظائف، مستخدمين)
- **نظام إشعارات محسن** مع تنبيهات الشبكة
- **اختصارات لوحة المفاتيح** (Ctrl+S للحفظ، Escape للإغلاق)
- **نظام إدارة المستندات الكامل** مع رفع وتصنيف وبحث
- **تذييل الجداول التفاعلي** مع إحصائيات مباشرة
- **نظام طباعة شامل ومحسن** لجميع التقارير والرواتب
- **طباعة قسائم الرواتب الفردية** بتصميم احترافي
- **🆕 نظام الإدارة المالية الشامل** مع العلاوات والغرامات والقروض
- **🆕 تقارير مالية متخصصة** قابلة للطباعة والتصدير
- **🆕 تكامل كامل مع نظام الرواتب** لحساب التأثير المالي
- **🆕 نظام إدارة الإجازات المتطور** مع تتبع الأرصدة والتقويم التفاعلي
- **🆕 إدارة أنواع الإجازات المختلفة** مع موافقة ورفض الطلبات

### 📱 تحسينات تجربة المستخدم
- **تصميم متجاوب محسن** للأجهزة المحمولة
- **تحميل تدريجي** للبيانات والصفحات
- **معالجة أخطاء متقدمة** مع رسائل واضحة
- **إمكانية وصول محسنة** لذوي الاحتياجات الخاصة
- **واجهة سحب وإفلات** للمستندات
- **معاينة فورية** للملفات المرفوعة

## التطوير المستقبلي

### الإصدار 1.2.0 (قريباً)
- [ ] نسخ احتياطي تلقائي للبيانات
- [ ] استيراد البيانات من Excel
- [ ] تقارير ذكية مع تحليلات متقدمة
- [ ] نظام إشعارات push
- [ ] دعم اللغات المتعددة

### الإصدار 1.3.0 (مستقبلي)
- [ ] تطبيق موبايل مصاحب
- [ ] تكامل مع أنظمة خارجية
- [ ] ذكاء اصطناعي للتنبؤات
- [ ] واجهة برمجة تطبيقات API

## الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل:
- راجع التوثيق في هذا الملف
- تحقق من وحدة تحكم المتصفح للأخطاء
- تأكد من دعم المتصفح لـ IndexedDB

## الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام والتطوير.

## اختصارات لوحة المفاتيح الجديدة ⌨️

| الاختصار | الوظيفة |
|----------|---------|
| `Ctrl + S` | حفظ سريع للنموذج النشط |
| `Escape` | إغلاق النوافذ المنبثقة |
| `Tab` | التنقل بين العناصر |
| `Enter` | تأكيد الإجراء |

## نصائح الاستخدام 💡

### للحصول على أفضل تجربة:
1. **استخدم متصفح حديث** (Chrome, Firefox, Safari, Edge)
2. **فعّل JavaScript** في المتصفح
3. **استخدم دقة شاشة 1024x768 أو أعلى**
4. **اعمل نسخ احتياطية دورية** للبيانات المهمة

### لتحسين الأداء:
- أغلق التبويبات غير المستخدمة
- امسح ذاكرة التخزين المؤقت دورياً
- استخدم وضع ملء الشاشة للجداول الكبيرة

## استكشاف الأخطاء وإصلاحها 🔧

### المشاكل الشائعة والحلول:

#### 1. لا تظهر البيانات
- **السبب**: مشكلة في قاعدة البيانات المحلية
- **الحل**: امسح بيانات الموقع وأعد تحميل الصفحة

#### 2. لا تعمل الأزرار
- **السبب**: JavaScript معطل أو خطأ في التحميل
- **الحل**: تأكد من تفعيل JavaScript وأعد تحميل الصفحة

#### 3. مشاكل في التصدير
- **السبب**: حاجب الإعلانات أو إعدادات الأمان
- **الحل**: أضف الموقع لقائمة المواقع الموثوقة

#### 4. بطء في الأداء
- **السبب**: كثرة البيانات أو ذاكرة ممتلئة
- **الحل**: امسح البيانات القديمة أو استخدم متصفح آخر

## الدعم الفني 📞

### للحصول على المساعدة:
1. **راجع هذا الدليل** أولاً
2. **تحقق من سجل الأخطاء** في وحدة تحكم المتصفح (F12)
3. **جرب في متصفح آخر** للتأكد من المشكلة
4. **تأكد من إصدار المتصفح** (يفضل الأحدث)

### معلومات النظام المطلوبة عند الإبلاغ عن مشكلة:
- نوع وإصدار المتصفح
- نظام التشغيل
- وصف تفصيلي للمشكلة
- خطوات إعادة إنتاج المشكلة

## ملاحظات مهمة

1. **الأمان**: البيانات محفوظة محلياً في المتصفح بتشفير آمن
2. **النسخ الاحتياطي**: يُنصح بعمل نسخ احتياطية أسبوعية
3. **التوافق**: يعمل على جميع المتصفحات الحديثة
4. **الأداء**: مُحسّن للشركات حتى 500 موظف
5. **التحديثات**: يتم إصدار تحديثات دورية مع ميزات جديدة

## إحصائيات النظام 📊

- **عدد الملفات**: 15+ ملف
- **عدد الوظائف**: 100+ وظيفة
- **عدد الصفحات**: 7 صفحات رئيسية
- **حجم النظام**: أقل من 2 ميجابايت
- **سرعة التحميل**: أقل من 3 ثواني
- **دعم الأجهزة**: كمبيوتر، تابلت، موبايل

---

## شكر وتقدير 🙏

تم تطوير هذا النظام بعناية فائقة ليكون حلاً شاملاً وسهل الاستخدام لإدارة الموظفين في الشركات الصغيرة والمتوسطة. نشكر جميع المستخدمين على ثقتهم واقتراحاتهم القيمة.

**الإصدار الحالي**: 1.5.0
**تاريخ آخر تحديث**: اليوم
**المطور**: فريق تطوير نظام إدارة الموظفين
