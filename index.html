<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة الموظفين</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="styles.css" rel="stylesheet">
</head>
<body class="login-page">
    <div class="container-fluid h-100">
        <div class="row h-100">
            <!-- Left Side - Login Form -->
            <div class="col-md-6 d-flex align-items-center justify-content-center">
                <div class="login-form-container">
                    <div class="text-center mb-4 fade-in">
                        <div class="logo-container mb-3">
                            <i class="fas fa-users-cog fa-4x text-primary pulse"></i>
                        </div>
                        <h2 class="fw-bold text-gradient">نظام إدارة الموظفين</h2>
                        <p class="text-muted">نظام شامل لإدارة ومحاسبة الموظفين</p>
                        <div class="version-badge">
                            <span class="badge bg-primary">الإصدار 1.0</span>
                        </div>
                    </div>
                    
                    <form id="loginForm" class="needs-validation" novalidate>
                        <div class="mb-3">
                            <label for="username" class="form-label">اسم المستخدم</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-user"></i></span>
                                <input type="text" class="form-control" id="username" required>
                                <div class="invalid-feedback">
                                    يرجى إدخال اسم المستخدم
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="password" class="form-label">كلمة المرور</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                <input type="password" class="form-control" id="password" required>
                                <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <div class="invalid-feedback">
                                    يرجى إدخال كلمة المرور
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="userRole" class="form-label">نوع المستخدم</label>
                            <select class="form-select" id="userRole" required>
                                <option value="">اختر نوع المستخدم</option>
                                <option value="admin">مدير النظام</option>
                                <option value="hr">مشرف الموارد البشرية</option>
                                <option value="accountant">محاسب</option>
                            </select>
                            <div class="invalid-feedback">
                                يرجى اختيار نوع المستخدم
                            </div>
                        </div>
                        
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="rememberMe">
                            <label class="form-check-label" for="rememberMe">
                                تذكرني
                            </label>
                        </div>
                        
                        <button type="submit" class="btn btn-primary w-100 mb-3">
                            <i class="fas fa-sign-in-alt me-2"></i>
                            تسجيل الدخول
                        </button>
                        
                        <div class="alert alert-danger d-none" id="loginError" role="alert">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <span id="loginErrorMessage"></span>
                        </div>
                    </form>
                    
                    <!-- Demo Accounts Info -->
                    <div class="demo-accounts mt-4">
                        <h6 class="text-muted mb-3">حسابات تجريبية:</h6>
                        <div class="row">
                            <div class="col-12 mb-2">
                                <small class="text-muted">
                                    <strong>مدير النظام:</strong> admin / admin123
                                </small>
                            </div>
                            <div class="col-12 mb-2">
                                <small class="text-muted">
                                    <strong>موارد بشرية:</strong> hr / hr123
                                </small>
                            </div>
                            <div class="col-12">
                                <small class="text-muted">
                                    <strong>محاسب:</strong> accountant / acc123
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Right Side - Features -->
            <div class="col-md-6 bg-primary text-white d-flex align-items-center">
                <div class="features-container p-5">
                    <h3 class="mb-4">مزايا النظام</h3>
                    <div class="feature-item mb-4">
                        <i class="fas fa-users fa-2x mb-2"></i>
                        <h5>إدارة شاملة للموظفين</h5>
                        <p>إضافة وتعديل وحذف بيانات الموظفين بسهولة</p>
                    </div>
                    <div class="feature-item mb-4">
                        <i class="fas fa-calculator fa-2x mb-2"></i>
                        <h5>حساب الرواتب التلقائي</h5>
                        <p>حساب الرواتب والخصومات والإضافي تلقائياً</p>
                    </div>
                    <div class="feature-item mb-4">
                        <i class="fas fa-chart-bar fa-2x mb-2"></i>
                        <h5>تقارير مفصلة</h5>
                        <p>تقارير شهرية وإحصائيات مفصلة</p>
                    </div>
                    <div class="feature-item mb-4">
                        <i class="fas fa-file-pdf fa-2x mb-2"></i>
                        <h5>تصدير PDF</h5>
                        <p>طباعة وتصدير التقارير بصيغة PDF</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="database.js"></script>
    <script src="auth.js"></script>
    <script src="app.js"></script>
    <script>
        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            initializeDatabase();
            setupLoginForm();
        });
    </script>
</body>
</html>
