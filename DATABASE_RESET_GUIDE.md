# دليل إعادة تهيئة قاعدة البيانات - نظام إدارة الموظفين

## 🔄 **مقدمة عن إعادة التهيئة**

إعادة تهيئة قاعدة البيانات هي عملية حذف جميع البيانات الحالية في النظام وإنشاء بيانات تجريبية جديدة شاملة لجميع أجزاء النظام. هذه العملية مفيدة للاختبار، التدريب، أو البدء من جديد.

---

## ⚠️ **تحذيرات مهمة**

### **🚨 قبل البدء:**
- **سيتم حذف جميع البيانات الحالية** ولا يمكن التراجع عن هذا الإجراء
- **تأكد من عمل نسخة احتياطية** إذا كنت تريد الاحتفاظ بالبيانات الحالية
- **هذه العملية مخصصة للاختبار والتطوير** وليس للاستخدام في بيئة الإنتاج
- **تأكد من إغلاق جميع النوافذ الأخرى** للنظام قبل البدء

---

## 🎯 **طرق إعادة التهيئة**

### **1. الطريقة الأولى: من لوحة التحكم**
1. اذهب إلى `dashboard.html`
2. انقر على زر **"إعادة التهيئة"** في أعلى الصفحة
3. أكد العملية في النافذة المنبثقة
4. انتظر حتى اكتمال العملية

### **2. الطريقة الثانية: صفحة إعادة التهيئة المخصصة**
1. اذهب إلى `database-reset.html`
2. راجع البيانات التي سيتم إنشاؤها
3. انقر على **"بدء إعادة التهيئة"**
4. تابع شريط التقدم حتى الانتهاء

### **3. الطريقة الثالثة: التشغيل المباشر**
1. اذهب إلى `run-reset.html`
2. ستبدأ العملية تلقائياً
3. انتظر حتى ظهور النتائج
4. انقر على "الذهاب إلى لوحة التحكم"

### **4. الطريقة الرابعة: من وحدة التحكم**
```javascript
// في وحدة تحكم المتصفح (F12)
resetDatabase();
```

---

## 📊 **البيانات التي سيتم إنشاؤها**

### **👥 الموظفين (8 موظفين)**
| الاسم | رقم الموظف | القسم | المنصب | الراتب |
|-------|------------|-------|--------|--------|
| أحمد محمد علي السالم | EMP001 | تقنية المعلومات | مطور برمجيات أول | 12,000 ريال |
| فاطمة أحمد السالم | EMP002 | الموارد البشرية | مدير الموارد البشرية | 10,000 ريال |
| محمد عبدالله الأحمد | EMP003 | المالية | محاسب أول | 9,000 ريال |
| نورا سعد المطيري | EMP004 | التسويق | أخصائي تسويق رقمي | 7,500 ريال |
| خالد عبدالرحمن القحطاني | EMP005 | المبيعات | مدير مبيعات | 11,000 ريال |
| سارة محمد الزهراني | EMP006 | خدمة العملاء | مشرف خدمة العملاء | 6,500 ريال |
| عبدالعزيز سالم الغامدي | EMP007 | الأمن والسلامة | مسؤول أمن وسلامة | 5,500 ريال |
| ريم عبدالله الدوسري | EMP008 | الشؤون الإدارية | منسق إداري | 6,000 ريال |

### **💰 الرواتب (3 سجلات)**
- سجلات رواتب للشهر الحالي
- حالات مختلفة: مدفوع، معلق
- تشمل العلاوات والخصومات

### **🏦 الإدارة المالية (7 عناصر)**
#### **العلاوات (3 عناصر):**
- علاوة أداء متميز: 1,000 ريال
- بدل مواصلات: 500 ريال  
- عمولة مبيعات: 2,000 ريال

#### **الغرامات (2 عناصر):**
- غرامة تأخير: 200 ريال
- غرامة غياب: 500 ريال

#### **القروض (2 عناصر):**
- قرض شخصي: 15,000 ريال
- قرض طوارئ: 8,000 ريال

### **🏖️ الإجازات (4 طلبات + أرصدة)**
#### **طلبات الإجازات:**
- إجازة سنوية معتمدة (5 أيام)
- إجازة مرضية معتمدة (3 أيام)
- إجازة طارئة معلقة (3 أيام)
- إجازة سنوية معلقة (7 أيام)

#### **أرصدة الإجازات:**
- رصيد سنوي: 30 يوم لكل موظف
- رصيد مرضي: 15 يوم لكل موظف
- تحديث تلقائي حسب الإجازات المستخدمة

### **⏰ الحضور والانصراف**
- سجلات الأسبوع الماضي لجميع الموظفين
- أوقات دخول وخروج متنوعة
- حساب ساعات العمل والإضافي
- استثناء عطل نهاية الأسبوع

### **⚙️ إعدادات النظام**
- معلومات الشركة الكاملة
- إعدادات الرواتب والإجازات
- إعدادات الحضور والانصراف
- إعدادات الإشعارات والنظام

---

## 🔧 **خطوات العملية التفصيلية**

### **المرحلة 1: التنظيف (10%)**
- حذف بيانات الموظفين الحالية
- حذف سجلات الرواتب
- حذف البيانات المالية
- حذف بيانات الإجازات والحضور
- حذف الإعدادات الحالية

### **المرحلة 2: إنشاء الموظفين (30%)**
- إنشاء 8 موظفين بأقسام مختلفة
- بيانات شخصية ووظيفية كاملة
- أرقام هوية وحسابات بنكية
- معلومات الاتصال والطوارئ

### **المرحلة 3: إنشاء الرواتب (50%)**
- حساب رواتب الشهر الحالي
- علاوات وخصومات متنوعة
- حالات دفع مختلفة

### **المرحلة 4: البيانات المالية (70%)**
- علاوات بأنواع مختلفة
- غرامات وخصومات
- قروض بشروط متنوعة

### **المرحلة 5: الإجازات (85%)**
- طلبات إجازات بحالات مختلفة
- أرصدة إجازات لجميع الموظفين
- حساب المستخدم والمتبقي

### **المرحلة 6: الحضور (95%)**
- سجلات حضور الأسبوع الماضي
- أوقات متنوعة وواقعية
- حساب ساعات العمل والإضافي

### **المرحلة 7: الإعدادات (100%)**
- إعدادات الشركة والنظام
- قواعد الرواتب والإجازات
- إعدادات الحضور والإشعارات

---

## ✅ **التحقق من نجاح العملية**

### **بعد إعادة التهيئة، تأكد من:**

1. **لوحة التحكم:**
   - عرض 8 موظفين
   - إحصائيات محدثة
   - رسوم بيانية تعمل

2. **إدارة الموظفين:**
   - ظهور جميع الموظفين الـ8
   - بيانات كاملة لكل موظف
   - إمكانية التعديل والحذف

3. **الرواتب:**
   - سجلات رواتب الشهر الحالي
   - حسابات صحيحة
   - حالات مختلفة

4. **الإدارة المالية:**
   - ظهور العلاوات والغرامات والقروض
   - قوائم الموظفين تعمل
   - إمكانية إضافة عناصر جديدة

5. **الإجازات:**
   - طلبات إجازات متنوعة
   - أرصدة صحيحة
   - قوائم الموظفين تعمل
   - التقويم يعرض الإجازات

6. **الحضور:**
   - سجلات الأسبوع الماضي
   - حسابات ساعات صحيحة
   - إمكانية إضافة سجلات جديدة

---

## 🚨 **استكشاف الأخطاء**

### **إذا لم تعمل العملية:**

1. **تحقق من وحدة التحكم (F12):**
   - ابحث عن رسائل الخطأ
   - تأكد من تحميل جميع الملفات

2. **امسح ذاكرة التخزين:**
   ```javascript
   localStorage.clear();
   location.reload();
   ```

3. **جرب طريقة أخرى:**
   - استخدم `run-reset.html` للتشغيل المباشر
   - أو استخدم وحدة التحكم

4. **تحقق من الملفات:**
   - تأكد من وجود `reset-database.js`
   - تأكد من تحميل جميع المكتبات

### **رسائل الخطأ الشائعة:**

- **"dbReset is not defined"**: لم يتم تحميل `reset-database.js`
- **"localStorage is not available"**: مشكلة في المتصفح
- **"Cannot read property"**: خطأ في البيانات

---

## 📝 **ملاحظات مهمة**

### **للمطورين:**
- يمكن تخصيص البيانات في `reset-database.js`
- إضافة موظفين أو تعديل البيانات الموجودة
- تغيير إعدادات النظام الافتراضية

### **للمستخدمين:**
- استخدم هذه الميزة للتدريب والاختبار
- لا تستخدمها في بيئة الإنتاج
- اعمل نسخة احتياطية قبل الاستخدام

### **للاختبار:**
- البيانات المنشأة واقعية ومتنوعة
- تغطي جميع حالات الاستخدام
- مناسبة لاختبار جميع ميزات النظام

---

## 🎯 **الخلاصة**

إعادة تهيئة قاعدة البيانات توفر:

✅ **بيانات شاملة ومتنوعة**  
✅ **اختبار جميع ميزات النظام**  
✅ **بيئة تدريب مثالية**  
✅ **بداية نظيفة للمشاريع الجديدة**  
✅ **بيانات واقعية ومنطقية**  

### **🚀 النظام جاهز للاستخدام الكامل بعد إعادة التهيئة!**

استمتع باستكشاف جميع ميزات نظام إدارة الموظفين مع البيانات التجريبية الشاملة! 🎉
