<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الموظفين - نظام إدارة الموظفين</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="styles.css" rel="stylesheet">
</head>
<body>
    <!-- Sidebar -->
    <nav class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <h4 class="text-white mb-0">
                <i class="fas fa-users-cog me-2"></i>
                <span class="sidebar-text">نظام الموظفين</span>
            </h4>
        </div>
        <ul class="sidebar-menu">
            <li>
                <a href="dashboard.html">
                    <i class="fas fa-tachometer-alt"></i>
                    <span class="sidebar-text">لوحة التحكم</span>
                </a>
            </li>
            <li>
                <a href="employees.html" class="active">
                    <i class="fas fa-users"></i>
                    <span class="sidebar-text">إدارة الموظفين</span>
                </a>
            </li>
            <li>
                <a href="payroll.html">
                    <i class="fas fa-calculator"></i>
                    <span class="sidebar-text">حساب الرواتب</span>
                </a>
            </li>
            <li>
                <a href="reports.html">
                    <i class="fas fa-chart-bar"></i>
                    <span class="sidebar-text">التقارير</span>
                </a>
            </li>
            <li>
                <a href="attendance.html">
                    <i class="fas fa-clock"></i>
                    <span class="sidebar-text">الحضور والانصراف</span>
                </a>
            </li>
            <li>
                <a href="settings.html">
                    <i class="fas fa-cog"></i>
                    <span class="sidebar-text">الإعدادات</span>
                </a>
            </li>
            <li>
                <a href="#" class="logout-btn">
                    <i class="fas fa-sign-out-alt"></i>
                    <span class="sidebar-text">تسجيل الخروج</span>
                </a>
            </li>
        </ul>
    </nav>

    <!-- Main Content -->
    <div class="main-content" id="mainContent">
        <!-- Header -->
        <header class="header">
            <div class="d-flex align-items-center">
                <button class="btn btn-link text-dark me-3" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h3 class="header-title">إدارة الموظفين</h3>
            </div>
            <div class="user-info">
                <span class="user-name me-2"></span>
                <span class="user-role badge bg-primary me-2"></span>
                <div class="user-avatar"></div>
            </div>
        </header>

        <!-- Content Area -->
        <div class="content-area">
            <!-- Search and Filter -->
            <div class="search-filter-container">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label for="searchInput" class="form-label">البحث</label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="searchInput" placeholder="البحث بالاسم أو الرقم الوظيفي">
                            <button class="btn btn-outline-secondary" type="button" id="searchBtn">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label for="departmentFilter" class="form-label">القسم</label>
                        <select class="form-select" id="departmentFilter">
                            <option value="">جميع الأقسام</option>
                        </select>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label for="statusFilter" class="form-label">الحالة</label>
                        <select class="form-select" id="statusFilter">
                            <option value="">جميع الحالات</option>
                            <option value="active">نشط</option>
                            <option value="inactive">غير نشط</option>
                            <option value="suspended">معلق</option>
                        </select>
                    </div>
                    <div class="col-md-2 mb-3">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button class="btn btn-secondary" id="clearFilters">
                                <i class="fas fa-times me-1"></i>
                                مسح
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Employees Table -->
            <div class="table-container">
                <div class="table-header">
                    <h5 class="mb-0">قائمة الموظفين</h5>
                    <div>
                        <div class="btn-group">
                            <button class="btn btn-success" id="addEmployeeBtn">
                                <i class="fas fa-plus me-1"></i>
                                إضافة موظف
                            </button>
                            <button class="btn btn-info dropdown-toggle" data-bs-toggle="dropdown">
                                <i class="fas fa-download me-1"></i>
                                تصدير
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#" id="exportPdfBtn">
                                    <i class="fas fa-file-pdf me-2"></i>تصدير PDF
                                </a></li>
                                <li><a class="dropdown-item" href="#" id="exportExcelBtn">
                                    <i class="fas fa-file-excel me-2"></i>تصدير Excel
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="#" id="printBtn">
                                    <i class="fas fa-print me-2"></i>طباعة
                                </a></li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="table-body">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead>
                                <tr>
                                    <th>
                                        <input type="checkbox" id="selectAll" class="form-check-input">
                                    </th>
                                    <th>الرقم الوظيفي</th>
                                    <th>الاسم</th>
                                    <th>القسم</th>
                                    <th>الوظيفة</th>
                                    <th>الراتب</th>
                                    <th>تاريخ التعيين</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="employeesTableBody">
                                <tr>
                                    <td colspan="9" class="text-center text-muted">جاري تحميل البيانات...</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="table-footer">
                    <div class="summary-info">
                        <div class="summary-item">
                            <span class="label">إجمالي الموظفين</span>
                            <span class="value" id="totalEmployees">0</span>
                        </div>
                        <div class="summary-item">
                            <span class="label">الموظفين النشطين</span>
                            <span class="value" id="activeEmployees">0</span>
                        </div>
                        <div class="summary-item">
                            <span class="label">إجمالي الرواتب</span>
                            <span class="value" id="totalSalaries">0 ر.س</span>
                        </div>
                        <div class="summary-item">
                            <span class="label">متوسط الراتب</span>
                            <span class="value" id="averageSalary">0 ر.س</span>
                        </div>
                    </div>
                    <div class="table-actions">
                        <button class="btn btn-outline-primary btn-sm" onclick="employeeManager.refreshData()">
                            <i class="fas fa-sync-alt"></i> تحديث
                        </button>
                        <button class="btn btn-outline-secondary btn-sm" data-bs-toggle="modal" data-bs-target="#documentModal">
                            <i class="fas fa-folder"></i> المستندات
                        </button>
                        <nav>
                            <ul class="pagination mb-0 pagination-sm" id="pagination">
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add/Edit Employee Modal -->
    <div class="modal fade" id="employeeModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="employeeModalTitle">إضافة موظف جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="employeeForm" class="needs-validation" novalidate>
                        <input type="hidden" id="employeeId">
                        
                        <!-- Personal Information -->
                        <div class="form-section">
                            <h5>المعلومات الشخصية</h5>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="employeeName" class="form-label">الاسم الكامل *</label>
                                    <input type="text" class="form-control" id="employeeName" required>
                                    <div class="invalid-feedback">يرجى إدخال الاسم الكامل</div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="employeeNumber" class="form-label">الرقم الوظيفي *</label>
                                    <input type="text" class="form-control" id="employeeNumber" required>
                                    <div class="invalid-feedback">يرجى إدخال الرقم الوظيفي</div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="nationalId" class="form-label">رقم الهوية</label>
                                    <input type="text" class="form-control" id="nationalId">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="phone" class="form-label">رقم الهاتف</label>
                                    <input type="tel" class="form-control" id="phone">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="email" class="form-label">البريد الإلكتروني</label>
                                    <input type="email" class="form-control" id="email">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="birthDate" class="form-label">تاريخ الميلاد</label>
                                    <input type="date" class="form-control" id="birthDate">
                                </div>
                            </div>
                        </div>

                        <!-- Job Information -->
                        <div class="form-section">
                            <h5>المعلومات الوظيفية</h5>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="department" class="form-label">القسم *</label>
                                    <select class="form-select" id="department" required>
                                        <option value="">اختر القسم</option>
                                    </select>
                                    <div class="invalid-feedback">يرجى اختيار القسم</div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="position" class="form-label">الوظيفة *</label>
                                    <select class="form-select" id="position" required>
                                        <option value="">اختر الوظيفة</option>
                                    </select>
                                    <div class="invalid-feedback">يرجى اختيار الوظيفة</div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="hireDate" class="form-label">تاريخ التعيين *</label>
                                    <input type="date" class="form-control" id="hireDate" required>
                                    <div class="invalid-feedback">يرجى إدخال تاريخ التعيين</div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="salary" class="form-label">الراتب الأساسي *</label>
                                    <input type="number" class="form-control" id="salary" min="0" step="0.01" required>
                                    <div class="invalid-feedback">يرجى إدخال الراتب الأساسي</div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="employeeStatus" class="form-label">الحالة *</label>
                                    <select class="form-select" id="employeeStatus" required>
                                        <option value="">اختر الحالة</option>
                                        <option value="active">نشط</option>
                                        <option value="inactive">غير نشط</option>
                                        <option value="suspended">معلق</option>
                                    </select>
                                    <div class="invalid-feedback">يرجى اختيار الحالة</div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="contractType" class="form-label">نوع العقد</label>
                                    <select class="form-select" id="contractType">
                                        <option value="permanent">دائم</option>
                                        <option value="temporary">مؤقت</option>
                                        <option value="contract">تعاقد</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Additional Information -->
                        <div class="form-section">
                            <h5>معلومات إضافية</h5>
                            <div class="row">
                                <div class="col-12 mb-3">
                                    <label for="address" class="form-label">العنوان</label>
                                    <textarea class="form-control" id="address" rows="2"></textarea>
                                </div>
                                <div class="col-12 mb-3">
                                    <label for="notes" class="form-label">ملاحظات</label>
                                    <textarea class="form-control" id="notes" rows="3"></textarea>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" form="employeeForm" class="btn btn-primary" id="saveEmployeeBtn">
                        <i class="fas fa-save me-1"></i>
                        حفظ
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title">تأكيد الحذف</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>هل أنت متأكد من حذف الموظف <strong id="deleteEmployeeName"></strong>؟</p>
                    <p class="text-muted">لا يمكن التراجع عن هذا الإجراء.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-danger" id="confirmDeleteBtn">
                        <i class="fas fa-trash me-1"></i>
                        حذف
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Document Management Modal -->
    <div class="modal fade" id="documentModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-folder-open me-2"></i>
                        إدارة المستندات
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <!-- Document Categories -->
                    <div class="document-categories">
                        <button class="category-filter active" data-category="all">
                            <i class="fas fa-list me-1"></i>جميع المستندات
                        </button>
                        <button class="category-filter" data-category="contracts">
                            <i class="fas fa-file-contract me-1"></i>العقود
                        </button>
                        <button class="category-filter" data-category="certificates">
                            <i class="fas fa-certificate me-1"></i>الشهادات
                        </button>
                        <button class="category-filter" data-category="reports">
                            <i class="fas fa-chart-line me-1"></i>التقارير
                        </button>
                        <button class="category-filter" data-category="personal">
                            <i class="fas fa-user me-1"></i>المستندات الشخصية
                        </button>
                    </div>

                    <!-- Document Search -->
                    <div class="document-search">
                        <input type="text" class="form-control" placeholder="البحث في المستندات..." id="documentSearchInput">
                        <i class="fas fa-search search-icon"></i>
                    </div>

                    <!-- Document Upload Area -->
                    <div class="document-upload-area" id="documentUploadArea">
                        <div class="document-upload-icon">
                            <i class="fas fa-cloud-upload-alt"></i>
                        </div>
                        <h5>رفع مستند جديد</h5>
                        <p class="text-muted">اسحب الملفات هنا أو انقر للاختيار</p>
                        <input type="file" id="documentFileInput" multiple accept=".pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png" style="display: none;">
                        <button class="btn btn-primary" onclick="document.getElementById('documentFileInput').click()">
                            <i class="fas fa-plus me-1"></i>اختيار ملفات
                        </button>
                    </div>

                    <!-- Document List -->
                    <div class="document-list" id="documentList">
                        <!-- سيتم ملء المستندات بواسطة JavaScript -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                    <button type="button" class="btn btn-primary" id="saveDocumentsBtn">
                        <i class="fas fa-save me-1"></i>حفظ التغييرات
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Document Preview Modal -->
    <div class="modal fade" id="documentPreviewModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="documentPreviewTitle">معاينة المستند</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="document-preview" id="documentPreviewContent">
                        <!-- سيتم عرض المستند هنا -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                    <button type="button" class="btn btn-primary" id="downloadDocumentBtn">
                        <i class="fas fa-download me-1"></i>تحميل
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="database.js"></script>
    <script src="auth.js"></script>
    <script src="document-manager.js"></script>
    <script src="employees.js"></script>
</body>
</html>
