<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التقارير - نظام إدارة الموظفين</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="styles.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <!-- Sidebar -->
    <nav class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <h4 class="text-white mb-0">
                <i class="fas fa-users-cog me-2"></i>
                <span class="sidebar-text">نظام الموظفين</span>
            </h4>
        </div>
        <ul class="sidebar-menu">
            <li>
                <a href="dashboard.html">
                    <i class="fas fa-tachometer-alt"></i>
                    <span class="sidebar-text">لوحة التحكم</span>
                </a>
            </li>
            <li>
                <a href="employees.html">
                    <i class="fas fa-users"></i>
                    <span class="sidebar-text">إدارة الموظفين</span>
                </a>
            </li>
            <li>
                <a href="payroll.html">
                    <i class="fas fa-calculator"></i>
                    <span class="sidebar-text">حساب الرواتب</span>
                </a>
            </li>
            <li>
                <a href="reports.html" class="active">
                    <i class="fas fa-chart-bar"></i>
                    <span class="sidebar-text">التقارير</span>
                </a>
            </li>
            <li>
                <a href="attendance.html">
                    <i class="fas fa-clock"></i>
                    <span class="sidebar-text">الحضور والانصراف</span>
                </a>
            </li>
            <li>
                <a href="settings.html">
                    <i class="fas fa-cog"></i>
                    <span class="sidebar-text">الإعدادات</span>
                </a>
            </li>
            <li>
                <a href="#" class="logout-btn">
                    <i class="fas fa-sign-out-alt"></i>
                    <span class="sidebar-text">تسجيل الخروج</span>
                </a>
            </li>
        </ul>
    </nav>

    <!-- Main Content -->
    <div class="main-content" id="mainContent">
        <!-- Header -->
        <header class="header">
            <div class="d-flex align-items-center">
                <button class="btn btn-link text-dark me-3" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h3 class="header-title">التقارير والإحصائيات</h3>
            </div>
            <div class="user-info">
                <span class="user-name me-2"></span>
                <span class="user-role badge bg-primary me-2"></span>
                <div class="user-avatar"></div>
            </div>
        </header>

        <!-- Content Area -->
        <div class="content-area">
            <!-- Report Type Selection -->
            <div class="form-container mb-4">
                <h5 class="mb-3">نوع التقرير</h5>
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label for="reportType" class="form-label">نوع التقرير</label>
                        <select class="form-select" id="reportType">
                            <option value="employees">تقرير الموظفين</option>
                            <option value="payroll">تقرير الرواتب</option>
                            <option value="departments">تقرير الأقسام</option>
                            <option value="attendance">تقرير الحضور</option>
                        </select>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label for="reportPeriod" class="form-label">الفترة</label>
                        <select class="form-select" id="reportPeriod">
                            <option value="current">الشهر الحالي</option>
                            <option value="last">الشهر الماضي</option>
                            <option value="quarter">الربع الحالي</option>
                            <option value="year">السنة الحالية</option>
                            <option value="custom">فترة مخصصة</option>
                        </select>
                    </div>
                    <div class="col-md-3 mb-3" id="customDateRange" style="display: none;">
                        <label for="dateFrom" class="form-label">من تاريخ</label>
                        <input type="date" class="form-control" id="dateFrom">
                    </div>
                    <div class="col-md-3 mb-3" id="customDateRangeTo" style="display: none;">
                        <label for="dateTo" class="form-label">إلى تاريخ</label>
                        <input type="date" class="form-control" id="dateTo">
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label for="departmentFilter" class="form-label">القسم</label>
                        <select class="form-select" id="departmentFilter">
                            <option value="">جميع الأقسام</option>
                        </select>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button class="btn btn-primary" id="generateReportBtn">
                                <i class="fas fa-chart-line me-1"></i>
                                إنشاء التقرير
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Report Content -->
            <div id="reportContent" style="display: none;">
                <!-- Report Header -->
                <div class="form-container mb-4">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h5 id="reportTitle">تقرير الموظفين</h5>
                            <p class="text-muted mb-0" id="reportSubtitle">الشهر الحالي</p>
                        </div>
                        <div>
                            <button class="btn btn-success me-2" id="exportExcelBtn">
                                <i class="fas fa-file-excel me-1"></i>
                                تصدير Excel
                            </button>
                            <button class="btn btn-danger" id="exportPdfBtn">
                                <i class="fas fa-file-pdf me-1"></i>
                                تصدير PDF
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Statistics Cards -->
                <div class="row mb-4" id="reportStats">
                    <!-- Stats will be populated dynamically -->
                </div>

                <!-- Charts Section -->
                <div class="row mb-4" id="reportCharts">
                    <!-- Charts will be populated dynamically -->
                </div>

                <!-- Data Table -->
                <div class="table-container" id="reportTable">
                    <div class="table-header">
                        <h5 class="mb-0" id="tableTitle">بيانات التقرير</h5>
                        <div class="d-flex gap-2">
                            <button class="btn btn-success btn-sm" onclick="reportsManager.exportCurrentReport('excel')">
                                <i class="fas fa-file-excel"></i> Excel
                            </button>
                            <button class="btn btn-danger btn-sm" onclick="reportsManager.exportCurrentReport('pdf')">
                                <i class="fas fa-file-pdf"></i> PDF
                            </button>
                            <button class="btn btn-primary btn-sm" onclick="reportsManager.printCurrentReport()">
                                <i class="fas fa-print"></i> طباعة
                            </button>
                        </div>
                    </div>
                    <div class="table-body">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead id="reportTableHead">
                                    <!-- Headers will be populated dynamically -->
                                </thead>
                                <tbody id="reportTableBody">
                                    <!-- Data will be populated dynamically -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="table-footer">
                        <div class="summary-info">
                            <div class="summary-item">
                                <span class="label">إجمالي السجلات</span>
                                <span class="value" id="totalRecords">0</span>
                            </div>
                            <div class="summary-item">
                                <span class="label">تاريخ التقرير</span>
                                <span class="value" id="reportDate">-</span>
                            </div>
                            <div class="summary-item">
                                <span class="label">نوع التقرير</span>
                                <span class="value" id="reportType">-</span>
                            </div>
                        </div>
                        <div class="table-actions">
                            <button class="btn btn-outline-primary btn-sm" onclick="reportsManager.refreshReport()">
                                <i class="fas fa-sync-alt"></i> تحديث
                            </button>
                            <button class="btn btn-outline-secondary btn-sm" onclick="reportsManager.saveReport()">
                                <i class="fas fa-save"></i> حفظ
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Reports -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="form-container">
                        <h5 class="mb-3">تقارير سريعة</h5>
                        <div class="row">
                            <div class="col-lg-3 col-md-6 mb-3">
                                <div class="card h-100">
                                    <div class="card-body text-center">
                                        <i class="fas fa-users fa-3x text-primary mb-3"></i>
                                        <h6 class="card-title">تقرير الموظفين النشطين</h6>
                                        <p class="card-text text-muted">قائمة بجميع الموظفين النشطين</p>
                                        <button class="btn btn-primary btn-sm" onclick="reportsManager.generateQuickReport('active-employees')">
                                            إنشاء التقرير
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-3 col-md-6 mb-3">
                                <div class="card h-100">
                                    <div class="card-body text-center">
                                        <i class="fas fa-money-bill-wave fa-3x text-success mb-3"></i>
                                        <h6 class="card-title">تقرير الرواتب الشهري</h6>
                                        <p class="card-text text-muted">ملخص رواتب الشهر الحالي</p>
                                        <button class="btn btn-success btn-sm" onclick="reportsManager.generateQuickReport('monthly-payroll')">
                                            إنشاء التقرير
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-3 col-md-6 mb-3">
                                <div class="card h-100">
                                    <div class="card-body text-center">
                                        <i class="fas fa-building fa-3x text-warning mb-3"></i>
                                        <h6 class="card-title">تقرير الأقسام</h6>
                                        <p class="card-text text-muted">إحصائيات الموظفين حسب القسم</p>
                                        <button class="btn btn-warning btn-sm" onclick="reportsManager.generateQuickReport('departments')">
                                            إنشاء التقرير
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-3 col-md-6 mb-3">
                                <div class="card h-100">
                                    <div class="card-body text-center">
                                        <i class="fas fa-chart-line fa-3x text-info mb-3"></i>
                                        <h6 class="card-title">تقرير الأداء</h6>
                                        <p class="card-text text-muted">تحليل أداء الموظفين</p>
                                        <button class="btn btn-info btn-sm" onclick="reportsManager.generateQuickReport('performance')">
                                            إنشاء التقرير
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="database.js"></script>
    <script src="auth.js"></script>
    <script src="reports.js"></script>
</body>
</html>
