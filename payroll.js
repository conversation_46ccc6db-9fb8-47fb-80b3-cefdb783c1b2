// Payroll Management
class PayrollManager {
    constructor() {
        this.employees = [];
        this.departments = [];
        this.payrollData = [];
        this.currentMonth = new Date().getMonth() + 1;
        this.currentYear = new Date().getFullYear();
        this.settings = {};
    }

    async init() {
        await this.loadData();
        this.setupEventListeners();
        this.initializeDateSelectors();
        this.setupSidebar();
    }

    async loadData() {
        try {
            this.employees = await dbManager.getAll('employees');
            this.departments = await dbManager.getAll('departments');
            this.settings = await this.loadSettings();
            
            this.populateDropdowns();
        } catch (error) {
            console.error('Error loading data:', error);
            this.showAlert('حدث خطأ في تحميل البيانات', 'danger');
        }
    }

    async loadSettings() {
        try {
            const settingsArray = await dbManager.getAll('settings');
            const settings = {};
            settingomrray.forEach(setting => {
                settings[setting.key] = setting.value;
            });
            return settings;
        } catch (error) {
            console.error('Error loading settings:', error);
            return {
                workingDaysPerMonth: 22,
                workingHoursPerDay: 8,
                overtimeRate: 1.5
            };
        }
    }

    populateDropdowns() {
        // Populate department filter
        const departmentFilter = document.getElementById('departmentFilter');
        departmentFilter.innerHTML = '<option value="">جميع الأقسام</option>';
        
        this.departments.forEach(dept => {
            departmentFilter.innerHTML += `<option value="${dept.name}">${dept.name}</option>`;
        });
    }

    initializeDateSelectors() {
        // Initialize year selector
        const yearSelect = document.getElementById('payrollYear');
        const currentYear = new Date().getFullYear();
        
        for (let year = currentYear - 2; year <= currentYear + 1; year++) {
            const option = document.createElement('option');
            option.value = year;
            option.textContent = year;
            if (year === currentYear) option.selected = true;
            yearSelect.appendChild(option);
        }

        // Set current month
        document.getElementById('payrollMonth').value = this.currentMonth;
    }

    setupEventListeners() {
        // Calculate payroll button
        document.getElementById('calculatePayrollBtn').addEventListener('click', () => this.calculatePayroll());

        // Save payroll button
        document.getElementById('savePayrollBtn').addEventListener('click', () => this.savePayroll());

        // Export payroll button
        document.getElementById('exportPayrollBtn').addEventListener('click', () => this.exportPayroll());

        // Save payroll details button
        document.getElementById('savePayrollDetailsBtn').addEventListener('click', () => this.savePayrollDetails());

        // Auto-calculate when payroll details change
        const payrollInputs = ['workingDays', 'overtimeHours', 'allowances', 'bonus', 'absenceDays', 'latePenalty', 'otherDeductions', 'insurance'];
        payrollInputs.forEach(inputId => {
            document.getElementById(inputId).addEventListener('input', () => this.calculatePayrollDetails());
        });
    }

    setupSidebar() {
        const sidebarToggle = document.getElementById('sidebarToggle');
        const sidebar = document.getElementById('sidebar');
        const mainContent = document.getElementById('mainContent');

        if (sidebarToggle) {
            sidebarToggle.addEventListener('click', () => {
                sidebar.classList.toggle('collapsed');
                mainContent.classList.toggle('expanded');
            });
        }
    }

    async calculatePayroll() {
        const month = parseInt(document.getElementById('payrollMonth').value);
        const year = parseInt(document.getElementById('payrollYear').value);
        const departmentFilter = document.getElementById('departmentFilter').value;

        try {
            // Filter employees
            let filteredEmployees = this.employees.filter(emp => emp.status === 'active');
            
            if (departmentFilter) {
                filteredEmployees = filteredEmployees.filter(emp => emp.department === departmentFilter);
            }

            if (filteredEmployees.length === 0) {
                this.showAlert('لا توجد موظفين نشطين في القسم المحدد', 'warning');
                return;
            }

            // Check if payroll already exists for this period
            const existingPayroll = await dbManager.getAllByIndex('payroll', 'month', month);
            const existingForYear = existingPayroll.filter(p => p.year === year);

            if (existingForYear.length > 0) {
                if (!confirm('يوجد كشف راتب لهذه الفترة. هل تريد إعادة الحساب؟')) {
                    return;
                }
            }

            // Calculate payroll for each employee
            this.payrollData = [];
            let totalBasicSalary = 0;
            let totalDeductions = 0;
            let totalNetSalary = 0;

            for (const employee of filteredEmployees) {
                const payrollItem = await this.calculateEmployeePayroll(employee, month, year);
                this.payrollData.push(payrollItem);
                
                totalBasicSalary += payrollItem.basicSalary;
                totalDeductions += payrollItem.totalDeductions;
                totalNetSalary += payrollItem.netSalary;
            }

            // Update summary
            this.updatePayrollSummary(filteredEmployees.length, totalBasicSalary, totalDeductions, totalNetSalary);

            // Render payroll table
            this.renderPayrollTable();

            // Show summary and table
            document.getElementById('payrollSummary').style.display = 'block';
            document.getElementById('payrollTable').style.display = 'block';

            this.showAlert('تم حساب الرواتب بنجاح', 'success');

        } catch (error) {
            console.error('Error calculating payroll:', error);
            this.showAlert('حدث خطأ في حساب الرواتب', 'danger');
        }
    }

    async calculateEmployeePayroll(employee, month, year) {
        // Get existing payroll data if available
        const existingPayroll = await this.getEmployeePayroll(employee.id, month, year);
        
        const workingDays = existingPayroll?.workingDays || this.settings.workingDaysPerMonth || 22;
        const overtimeHours = existingPayroll?.overtimeHours || 0;
        const allowances = existingPayroll?.allowances || 0;
        const bonus = existingPayroll?.bonus || 0;
        const absenceDays = existingPayroll?.absenceDays || 0;
        const latePenalty = existingPayroll?.latePenalty || 0;
        const otherDeductions = existingPayroll?.otherDeductions || 0;
        const insurance = existingPayroll?.insurance || (employee.salary * 0.09); // 9% social insurance

        // Calculate basic salary based on working days
        const dailySalary = employee.salary / (this.settings.workingDaysPerMonth || 22);
        const basicSalaryForPeriod = dailySalary * workingDays;

        // Calculate overtime pay
        const hourlyRate = employee.salary / ((this.settings.workingDaysPerMonth || 22) * (this.settings.workingHoursPerDay || 8));
        const overtimePay = overtimeHours * hourlyRate * (this.settings.overtimeRate || 1.5);

        // Calculate absence deduction
        const absenceDeduction = dailySalary * absenceDays;

        // Calculate totals
        const totalAdditions = basicSalaryForPeriod + overtimePay + allowances + bonus;
        const totalDeductions = absenceDeduction + latePenalty + otherDeductions + insurance;
        const netSalary = totalAdditions - totalDeductions;

        return {
            employeeId: employee.id,
            employeeNumber: employee.employeeNumber,
            employeeName: employee.name,
            department: employee.department,
            basicSalary: employee.salary,
            workingDays,
            overtimeHours,
            allowances,
            bonus,
            absenceDays,
            latePenalty,
            otherDeductions,
            insurance,
            totalAdditions,
            totalDeductions,
            netSalary,
            month,
            year,
            calculatedAt: new Date().toISOString()
        };
    }

    async getEmployeePayroll(employeeId, month, year) {
        try {
            const payrollRecords = await dbManager.getAllByIndex('payroll', 'employeeId', employeeId);
            return payrollRecords.find(record => record.month === month && record.year === year);
        } catch (error) {
            console.error('Error getting employee payroll:', error);
            return null;
        }
    }

    updatePayrollSummary(employeeCount, totalBasic, totalDeductions, totalNet) {
        document.getElementById('totalEmployeesCount').textContent = employeeCount;
        document.getElementById('totalBasicSalary').textContent = this.formatCurrency(totalBasic);
        document.getElementById('totalDeductions').textContent = this.formatCurrency(totalDeductions);
        document.getElementById('totalNetSalary').textContent = this.formatCurrency(totalNet);
    }

    renderPayrollTable() {
        const tbody = document.getElementById('payrollTableBody');
        
        if (this.payrollData.length === 0) {
            tbody.innerHTML = '<tr><td colspan="10" class="text-center text-muted">لا توجد بيانات</td></tr>';
            return;
        }

        tbody.innerHTML = this.payrollData.map(item => `
            <tr>
                <td>${item.employeeNumber}</td>
                <td>${item.employeeName}</td>
                <td>${item.department}</td>
                <td>${this.formatCurrency(item.basicSalary)}</td>
                <td>${item.workingDays}</td>
                <td>${item.overtimeHours}</td>
                <td>${this.formatCurrency(item.allowances + item.bonus)}</td>
                <td>${this.formatCurrency(item.totalDeductions)}</td>
                <td><strong>${this.formatCurrency(item.netSalary)}</strong></td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="payrollManager.editPayrollDetails(${item.employeeId})" title="تعديل">
                        <i class="fas fa-edit"></i>
                    </button>
                </td>
            </tr>
        `).join('');
    }

    editPayrollDetails(employeeId) {
        const employee = this.employees.find(emp => emp.id === employeeId);
        const payrollItem = this.payrollData.find(item => item.employeeId === employeeId);
        
        if (!employee || !payrollItem) return;

        // Populate modal
        document.getElementById('employeeIdForPayroll').value = employeeId;
        document.getElementById('employeeNameForPayroll').textContent = employee.name;
        document.getElementById('employeeDepartmentForPayroll').textContent = employee.department;
        document.getElementById('basicSalaryDisplay').textContent = this.formatCurrency(employee.salary);

        // Populate form fields
        document.getElementById('workingDays').value = payrollItem.workingDays;
        document.getElementById('overtimeHours').value = payrollItem.overtimeHours;
        document.getElementById('allowances').value = payrollItem.allowances;
        document.getElementById('bonus').value = payrollItem.bonus;
        document.getElementById('absenceDays').value = payrollItem.absenceDays;
        document.getElementById('latePenalty').value = payrollItem.latePenalty;
        document.getElementById('otherDeductions').value = payrollItem.otherDeductions;
        document.getElementById('insurance').value = payrollItem.insurance;

        // Calculate and show totals
        this.calculatePayrollDetails();

        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('payrollDetailsModal'));
        modal.show();
    }

    calculatePayrollDetails() {
        const basicSalary = parseFloat(document.getElementById('basicSalaryDisplay').textContent.replace(/[^\d.-]/g, ''));
        const workingDays = parseFloat(document.getElementById('workingDays').value) || 0;
        const overtimeHours = parseFloat(document.getElementById('overtimeHours').value) || 0;
        const allowances = parseFloat(document.getElementById('allowances').value) || 0;
        const bonus = parseFloat(document.getElementById('bonus').value) || 0;
        const absenceDays = parseFloat(document.getElementById('absenceDays').value) || 0;
        const latePenalty = parseFloat(document.getElementById('latePenalty').value) || 0;
        const otherDeductions = parseFloat(document.getElementById('otherDeductions').value) || 0;
        const insurance = parseFloat(document.getElementById('insurance').value) || 0;

        // Calculate based on working days
        const dailySalary = basicSalary / (this.settings.workingDaysPerMonth || 22);
        const basicSalaryForPeriod = dailySalary * workingDays;

        // Calculate overtime
        const hourlyRate = basicSalary / ((this.settings.workingDaysPerMonth || 22) * (this.settings.workingHoursPerDay || 8));
        const overtimePay = overtimeHours * hourlyRate * (this.settings.overtimeRate || 1.5);

        // Calculate absence deduction
        const absenceDeduction = dailySalary * absenceDays;

        // Calculate totals
        const totalAdditions = basicSalaryForPeriod + overtimePay + allowances + bonus;
        const totalDeductionsCalc = absenceDeduction + latePenalty + otherDeductions + insurance;
        const netSalary = totalAdditions - totalDeductionsCalc;

        // Update display
        document.getElementById('totalAdditions').textContent = this.formatCurrency(totalAdditions);
        document.getElementById('totalDeductionsCalc').textContent = this.formatCurrency(totalDeductionsCalc);
        document.getElementById('netSalaryCalc').textContent = this.formatCurrency(netSalary);
    }

    // Utility functions
    formatCurrency(amount) {
        return new Intl.NumberFormat('ar-SA', {
            style: 'currency',
            currency: 'omr',
            minimumFractionDigits: 0
        }).format(amount || 0);
    }

    savePayrollDetails() {
        const employeeId = parseInt(document.getElementById('employeeIdForPayroll').value);
        const payrollItemIndex = this.payrollData.findIndex(item => item.employeeId === employeeId);

        if (payrollItemIndex === -1) return;

        // Update payroll data
        const payrollItem = this.payrollData[payrollItemIndex];
        payrollItem.workingDays = parseFloat(document.getElementById('workingDays').value) || 0;
        payrollItem.overtimeHours = parseFloat(document.getElementById('overtimeHours').value) || 0;
        payrollItem.allowances = parseFloat(document.getElementById('allowances').value) || 0;
        payrollItem.bonus = parseFloat(document.getElementById('bonus').value) || 0;
        payrollItem.absenceDays = parseFloat(document.getElementById('absenceDays').value) || 0;
        payrollItem.latePenalty = parseFloat(document.getElementById('latePenalty').value) || 0;
        payrollItem.otherDeductions = parseFloat(document.getElementById('otherDeductions').value) || 0;
        payrollItem.insurance = parseFloat(document.getElementById('insurance').value) || 0;

        // Recalculate totals
        const employee = this.employees.find(emp => emp.id === employeeId);
        const dailySalary = employee.salary / (this.settings.workingDaysPerMonth || 22);
        const basicSalaryForPeriod = dailySalary * payrollItem.workingDays;

        const hourlyRate = employee.salary / ((this.settings.workingDaysPerMonth || 22) * (this.settings.workingHoursPerDay || 8));
        const overtimePay = payrollItem.overtimeHours * hourlyRate * (this.settings.overtimeRate || 1.5);

        const absenceDeduction = dailySalary * payrollItem.absenceDays;

        payrollItem.totalAdditions = basicSalaryForPeriod + overtimePay + payrollItem.allowances + payrollItem.bonus;
        payrollItem.totalDeductions = absenceDeduction + payrollItem.latePenalty + payrollItem.otherDeductions + payrollItem.insurance;
        payrollItem.netSalary = payrollItem.totalAdditions - payrollItem.totalDeductions;

        // Update summary
        const totalBasic = this.payrollData.reduce((sum, item) => sum + item.basicSalary, 0);
        const totalDeductions = this.payrollData.reduce((sum, item) => sum + item.totalDeductions, 0);
        const totalNet = this.payrollData.reduce((sum, item) => sum + item.netSalary, 0);

        this.updatePayrollSummary(this.payrollData.length, totalBasic, totalDeductions, totalNet);

        // Re-render table
        this.renderPayrollTable();

        // Close modal
        bootstrap.Modal.getInstance(document.getElementById('payrollDetailsModal')).hide();

        this.showAlert('تم تحديث تفاصيل الراتب بنجاح', 'success');
    }

    async savePayroll() {
        if (this.payrollData.length === 0) {
            this.showAlert('لا توجد بيانات رواتب للحفظ', 'warning');
            return;
        }

        try {
            // Save each payroll record
            for (const payrollItem of this.payrollData) {
                // Check if record already exists
                const existingRecord = await this.getEmployeePayroll(payrollItem.employeeId, payrollItem.month, payrollItem.year);

                if (existingRecord) {
                    // Update existing record
                    payrollItem.id = existingRecord.id;
                    await dbManager.update('payroll', payrollItem);
                } else {
                    // Add new record
                    await dbManager.add('payroll', payrollItem);
                }
            }

            this.showAlert('تم حفظ كشف الرواتب بنجاح', 'success');

        } catch (error) {
            console.error('Error saving payroll:', error);
            this.showAlert('حدث خطأ في حفظ كشف الرواتب', 'danger');
        }
    }

    async exportPayroll() {
        if (this.payrollData.length === 0) {
            this.showAlert('لا توجد بيانات رواتب للتصدير', 'warning');
            return;
        }

        try {
            const { jsPDF } = window.jspdf;
            const doc = new jsPDF('landscape');

            // Add title
            doc.setFontSize(16);
            const month = document.getElementById('payrollMonth').value;
            const year = document.getElementById('payrollYear').value;
            const monthNames = ['', 'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'];

            doc.text(`Payroll Report - ${monthNames[month]} ${year}`, 20, 20);

            // Add table headers
            let yPosition = 40;
            const headers = ['Emp#', 'Name', 'Department', 'Basic', 'Days', 'OT', 'Allow', 'Deduct', 'Net'];

            doc.setFontSize(10);
            headers.forEach((header, index) => {
                doc.text(header, 20 + (index * 30), yPosition);
            });

            // Add payroll data
            yPosition += 10;
            let totalNet = 0;

            this.payrollData.forEach((item) => {
                if (yPosition > 180) {
                    doc.addPage();
                    yPosition = 20;
                }

                const row = [
                    item.employeeNumber,
                    item.employeeName.substring(0, 12),
                    item.department.substring(0, 8),
                    item.basicSalary.toFixed(0),
                    item.workingDays.toString(),
                    item.overtimeHours.toString(),
                    (item.allowances + item.bonus).toFixed(0),
                    item.totalDeductions.toFixed(0),
                    item.netSalary.toFixed(0)
                ];

                row.forEach((cell, cellIndex) => {
                    doc.text(cell, 20 + (cellIndex * 30), yPosition);
                });

                totalNet += item.netSalary;
                yPosition += 8;
            });

            // Add total
            yPosition += 10;
            doc.setFontSize(12);
            doc.text(`Total Net Salary: ${totalNet.toFixed(2)} omr`, 20, yPosition);

            // Save the PDF
            doc.save(`payroll-${monthNames[month]}-${year}.pdf`);
            this.showAlert('تم تصدير كشف الرواتب بنجاح', 'success');

        } catch (error) {
            console.error('Error exporting payroll:', error);
            this.showAlert('حدث خطأ في تصدير كشف الرواتب', 'danger');
        }
    }

    showAlert(message, type = 'info') {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        alertDiv.style.cssText = 'top: 20px; left: 20px; z-index: 9999; min-width: 300px;';
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(alertDiv);

        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 5000);
    }
}

// Global instance
const payrollManager = new PayrollManager();

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', async function() {
    await initializeDatabase();
    await payrollManager.init();
});
