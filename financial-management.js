// Financial Management System
class FinancialManager {
    constructor() {
        this.allowances = [];
        this.penalties = [];
        this.loans = [];
        this.employees = [];
        this.currentEditId = null;
        this.currentEditType = null;
        this.financialChart = null;
    }

    async init() {
        await this.loadData();
        this.setupEventListeners();
        this.renderAllTables();
        this.updateSummaryCards();
        this.renderFinancialChart();
    }

    async loadData() {
        try {
            // Load employees first
            await this.loadEmployees();

            // Load financial data
            const allowancesData = localStorage.getItem('allowances');
            this.allowances = allowancesData ? JSON.parse(allowancesData) : [];

            const penaltiesData = localStorage.getItem('penalties');
            this.penalties = penaltiesData ? JSON.parse(penaltiesData) : [];

            const loansData = localStorage.getItem('loans');
            this.loans = loansData ? JSON.parse(loansData) : [];

            // Populate employee dropdowns
            this.populateEmployeeDropdowns();

        } catch (error) {
            console.error('Error loading financial data:', error);
            this.showAlert('حدث خطأ في تحميل البيانات المالية', 'danger');
        }
    }

    async loadEmployees() {
        try {
            // Try to load from localStorage first
            const employeesData = localStorage.getItem('employees');
            if (employeesData) {
                this.employees = JSON.parse(employeesData);
                console.log('Loaded employees from localStorage:', this.employees.length);
            }

            // If no employees found, create sample data
            if (this.employees.length === 0) {
                console.log('No employees found, creating sample data...');
                this.createSampleEmployees();
            }

        } catch (error) {
            console.error('Error loading employees:', error);
            this.createSampleEmployees();
        }
    }

    createSampleEmployees() {
        this.employees = [
            {
                id: '1',
                name: 'أحمد محمد علي',
                employeeNumber: 'EMP001',
                department: 'تقنية المعلومات',
                position: 'مطور برمجيات',
                salary: 8000,
                email: '<EMAIL>',
                phone: '0501234567',
                hireDate: '2023-01-15',
                status: 'active'
            },
            {
                id: '2',
                name: 'فاطمة أحمد السالم',
                employeeNumber: 'EMP002',
                department: 'الموارد البشرية',
                position: 'أخصائي موارد بشرية',
                salary: 6500,
                email: '<EMAIL>',
                phone: '0507654321',
                hireDate: '2023-02-01',
                status: 'active'
            },
            {
                id: '3',
                name: 'محمد عبدالله الأحمد',
                employeeNumber: 'EMP003',
                department: 'المالية',
                position: 'محاسب',
                salary: 7000,
                email: '<EMAIL>',
                phone: '0509876543',
                hireDate: '2023-03-10',
                status: 'active'
            },
            {
                id: '4',
                name: 'نورا سعد المطيري',
                employeeNumber: 'EMP004',
                department: 'التسويق',
                position: 'أخصائي تسويق',
                salary: 5500,
                email: '<EMAIL>',
                phone: '0502468135',
                hireDate: '2023-04-05',
                status: 'active'
            },
            {
                id: '5',
                name: 'خالد عبدالرحمن القحطاني',
                employeeNumber: 'EMP005',
                department: 'المبيعات',
                position: 'مندوب مبيعات',
                salary: 6000,
                email: '<EMAIL>',
                phone: '0503691472',
                hireDate: '2023-05-20',
                status: 'active'
            }
        ];

        // Save to localStorage
        localStorage.setItem('employees', JSON.stringify(this.employees));
        console.log('Created and saved sample employees:', this.employees.length);
    }

    populateEmployeeDropdowns() {
        const dropdowns = ['allowanceEmployee', 'penaltyEmployee', 'loanEmployee'];

        console.log('Populating financial dropdowns with employees:', this.employees.length);

        dropdowns.forEach(dropdownId => {
            const dropdown = document.getElementById(dropdownId);
            if (dropdown) {
                dropdown.innerHTML = '<option value="">اختر الموظف</option>';

                if (this.employees && this.employees.length > 0) {
                    this.employees.forEach(employee => {
                        const option = document.createElement('option');
                        option.value = employee.id;
                        option.textContent = `${employee.name} - ${employee.employeeNumber || employee.department}`;
                        dropdown.appendChild(option);
                    });
                    console.log(`Dropdown ${dropdownId} populated successfully`);
                } else {
                    console.log(`No employees found for dropdown ${dropdownId}`);
                    const option = document.createElement('option');
                    option.value = '';
                    option.textContent = 'لا توجد موظفين - يرجى إضافة موظفين أولاً';
                    option.disabled = true;
                    dropdown.appendChild(option);
                }
            } else {
                console.log(`Dropdown ${dropdownId} not found`);
            }
        });
    }

    setupEventListeners() {
        // Save buttons
        document.getElementById('saveAllowanceBtn').addEventListener('click', () => this.saveAllowance());
        document.getElementById('savePenaltyBtn').addEventListener('click', () => this.savePenalty());
        document.getElementById('saveLoanBtn').addEventListener('click', () => this.saveLoan());

        // Delete confirmation
        document.getElementById('confirmDeleteFinancialBtn').addEventListener('click', () => this.confirmDelete());

        // Form calculations
        document.getElementById('loanAmount').addEventListener('input', () => this.calculateLoanInstallment());
        document.getElementById('loanDuration').addEventListener('input', () => this.calculateLoanInstallment());
        document.getElementById('loanInterestRate').addEventListener('input', () => this.calculateLoanInstallment());

        // Penalty deduction type change
        document.getElementById('penaltyDeductionType').addEventListener('change', (e) => {
            const installmentsField = document.getElementById('installmentsField');
            if (e.target.value === 'installments') {
                installmentsField.style.display = 'block';
                document.getElementById('penaltyInstallments').required = true;
            } else {
                installmentsField.style.display = 'none';
                document.getElementById('penaltyInstallments').required = false;
            }
        });

        // Set default dates
        const today = new Date().toISOString().split('T')[0];
        document.getElementById('allowanceDate').value = today;
        document.getElementById('penaltyDate').value = today;
        document.getElementById('loanDate').value = today;

        // Set current month and year for reports
        const currentDate = new Date();
        const reportMonth = document.getElementById('reportMonth');
        const reportYear = document.getElementById('reportYear');

        if (reportMonth) {
            reportMonth.value = currentDate.getMonth() + 1;
        }

        if (reportYear) {
            reportYear.value = currentDate.getFullYear();
        }

        // Modal events to refresh employee dropdowns
        const allowanceModal = document.getElementById('allowanceModal');
        const penaltyModal = document.getElementById('penaltyModal');
        const loanModal = document.getElementById('loanModal');

        if (allowanceModal) {
            allowanceModal.addEventListener('show.bs.modal', () => {
                this.populateEmployeeDropdowns();
            });
        }

        if (penaltyModal) {
            penaltyModal.addEventListener('show.bs.modal', () => {
                this.populateEmployeeDropdowns();
            });
        }

        if (loanModal) {
            loanModal.addEventListener('show.bs.modal', () => {
                this.populateEmployeeDropdowns();
            });
        }
    }

    calculateLoanInstallment() {
        const amount = parseFloat(document.getElementById('loanAmount').value) || 0;
        const duration = parseInt(document.getElementById('loanDuration').value) || 1;
        const interestRate = parseFloat(document.getElementById('loanInterestRate').value) || 0;

        if (amount > 0 && duration > 0) {
            let monthlyInstallment;
            
            if (interestRate > 0) {
                // Calculate with interest
                const monthlyRate = interestRate / 100 / 12;
                monthlyInstallment = amount * (monthlyRate * Math.pow(1 + monthlyRate, duration)) / 
                                   (Math.pow(1 + monthlyRate, duration) - 1);
            } else {
                // Simple division without interest
                monthlyInstallment = amount / duration;
            }

            document.getElementById('monthlyInstallment').value = monthlyInstallment.toFixed(2);
        }
    }

    // Allowances Management
    saveAllowance() {
        const form = document.getElementById('allowanceForm');
        if (!form.checkValidity()) {
            form.reportValidity();
            return;
        }

        const allowanceData = {
            id: this.currentEditId || Date.now().toString(),
            employeeId: document.getElementById('allowanceEmployee').value,
            type: document.getElementById('allowanceType').value,
            amount: parseFloat(document.getElementById('allowanceAmount').value),
            date: document.getElementById('allowanceDate').value,
            frequency: document.getElementById('allowanceFrequency').value,
            status: document.getElementById('allowanceStatus').value,
            notes: document.getElementById('allowanceNotes').value,
            createdAt: this.currentEditId ? this.allowances.find(a => a.id === this.currentEditId)?.createdAt : new Date().toISOString()
        };

        if (this.currentEditId) {
            // Update existing allowance
            const index = this.allowances.findIndex(a => a.id === this.currentEditId);
            if (index !== -1) {
                this.allowances[index] = allowanceData;
            }
        } else {
            // Add new allowance
            this.allowances.push(allowanceData);
        }

        this.saveToStorage('allowances', this.allowances);
        this.renderAllowancesTable();
        this.updateSummaryCards();
        this.renderFinancialChart();

        // Close modal and reset form
        const modal = bootstrap.Modal.getInstance(document.getElementById('allowanceModal'));
        modal.hide();
        this.resetAllowanceForm();

        this.showAlert(this.currentEditId ? 'تم تحديث العلاوة بنجاح' : 'تم إضافة العلاوة بنجاح', 'success');
        this.currentEditId = null;
    }

    // Penalties Management
    savePenalty() {
        const form = document.getElementById('penaltyForm');
        if (!form.checkValidity()) {
            form.reportValidity();
            return;
        }

        const penaltyData = {
            id: this.currentEditId || Date.now().toString(),
            employeeId: document.getElementById('penaltyEmployee').value,
            type: document.getElementById('penaltyType').value,
            amount: parseFloat(document.getElementById('penaltyAmount').value),
            date: document.getElementById('penaltyDate').value,
            deductionType: document.getElementById('penaltyDeductionType').value,
            installments: document.getElementById('penaltyInstallments').value || 1,
            reason: document.getElementById('penaltyReason').value,
            status: document.getElementById('penaltyStatus').value,
            createdAt: this.currentEditId ? this.penalties.find(p => p.id === this.currentEditId)?.createdAt : new Date().toISOString()
        };

        if (this.currentEditId) {
            // Update existing penalty
            const index = this.penalties.findIndex(p => p.id === this.currentEditId);
            if (index !== -1) {
                this.penalties[index] = penaltyData;
            }
        } else {
            // Add new penalty
            this.penalties.push(penaltyData);
        }

        this.saveToStorage('penalties', this.penalties);
        this.renderPenaltiesTable();
        this.updateSummaryCards();
        this.renderFinancialChart();

        // Close modal and reset form
        const modal = bootstrap.Modal.getInstance(document.getElementById('penaltyModal'));
        modal.hide();
        this.resetPenaltyForm();

        this.showAlert(this.currentEditId ? 'تم تحديث الغرامة بنجاح' : 'تم إضافة الغرامة بنجاح', 'success');
        this.currentEditId = null;
    }

    // Loans Management
    saveLoan() {
        const form = document.getElementById('loanForm');
        if (!form.checkValidity()) {
            form.reportValidity();
            return;
        }

        const loanData = {
            id: this.currentEditId || Date.now().toString(),
            employeeId: document.getElementById('loanEmployee').value,
            type: document.getElementById('loanType').value,
            amount: parseFloat(document.getElementById('loanAmount').value),
            date: document.getElementById('loanDate').value,
            duration: parseInt(document.getElementById('loanDuration').value),
            interestRate: parseFloat(document.getElementById('loanInterestRate').value) || 0,
            monthlyInstallment: parseFloat(document.getElementById('monthlyInstallment').value),
            paidAmount: this.currentEditId ? this.loans.find(l => l.id === this.currentEditId)?.paidAmount || 0 : 0,
            purpose: document.getElementById('loanPurpose').value,
            notes: document.getElementById('loanNotes').value,
            status: document.getElementById('loanStatus').value,
            createdAt: this.currentEditId ? this.loans.find(l => l.id === this.currentEditId)?.createdAt : new Date().toISOString()
        };

        if (this.currentEditId) {
            // Update existing loan
            const index = this.loans.findIndex(l => l.id === this.currentEditId);
            if (index !== -1) {
                this.loans[index] = loanData;
            }
        } else {
            // Add new loan
            this.loans.push(loanData);
        }

        this.saveToStorage('loans', this.loans);
        this.renderLoansTable();
        this.updateSummaryCards();
        this.renderFinancialChart();

        // Close modal and reset form
        const modal = bootstrap.Modal.getInstance(document.getElementById('loanModal'));
        modal.hide();
        this.resetLoanForm();

        this.showAlert(this.currentEditId ? 'تم تحديث القرض بنجاح' : 'تم إضافة القرض بنجاح', 'success');
        this.currentEditId = null;
    }

    saveToStorage(key, data) {
        try {
            localStorage.setItem(key, JSON.stringify(data));
        } catch (error) {
            console.error(`Error saving ${key}:`, error);
            this.showAlert(`حدث خطأ في حفظ ${key}`, 'danger');
        }
    }

    getEmployeeName(employeeId) {
        const employee = this.employees.find(emp => emp.id === employeeId);
        return employee ? employee.name : 'غير محدد';
    }

    formatCurrency(amount) {
        return new Intl.NumberFormat('ar-SA', {
            style: 'currency',
            currency: 'SAR',
            minimumFractionDigits: 2
        }).format(amount);
    }

    formatDate(dateString) {
        return new Date(dateString).toLocaleDateString('ar-SA');
    }

    getStatusBadge(status, type) {
        const statusConfig = {
            allowance: {
                active: 'success',
                pending: 'warning',
                suspended: 'secondary',
                cancelled: 'danger'
            },
            penalty: {
                active: 'danger',
                pending: 'warning',
                paid: 'success',
                cancelled: 'secondary'
            },
            loan: {
                active: 'primary',
                pending: 'warning',
                completed: 'success',
                cancelled: 'secondary'
            }
        };

        const statusText = {
            active: 'نشط',
            pending: 'في الانتظار',
            suspended: 'معلق',
            cancelled: 'ملغي',
            paid: 'مدفوع',
            completed: 'مكتمل'
        };

        const badgeClass = statusConfig[type][status] || 'secondary';
        const text = statusText[status] || status;

        return `<span class="badge bg-${badgeClass}">${text}</span>`;
    }

    showAlert(message, type = 'info') {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        alertDiv.style.cssText = 'top: 20px; left: 20px; z-index: 9999; min-width: 300px;';
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(alertDiv);

        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 5000);
    }

    // Render Tables
    renderAllTables() {
        this.renderAllowancesTable();
        this.renderPenaltiesTable();
        this.renderLoansTable();
    }

    renderAllowancesTable() {
        const tbody = document.getElementById('allowancesTable');
        if (!tbody) return;

        if (this.allowances.length === 0) {
            tbody.innerHTML = '<tr><td colspan="6" class="text-center text-muted">لا توجد علاوات مسجلة</td></tr>';
            return;
        }

        tbody.innerHTML = this.allowances.map(allowance => `
            <tr>
                <td>${this.getEmployeeName(allowance.employeeId)}</td>
                <td>${this.getAllowanceTypeText(allowance.type)}</td>
                <td>${this.formatCurrency(allowance.amount)}</td>
                <td>${this.formatDate(allowance.date)}</td>
                <td>${this.getStatusBadge(allowance.status, 'allowance')}</td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="financialManager.editAllowance('${allowance.id}')" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="financialManager.deleteItem('${allowance.id}', 'allowance')" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }

    renderPenaltiesTable() {
        const tbody = document.getElementById('penaltiesTable');
        if (!tbody) return;

        if (this.penalties.length === 0) {
            tbody.innerHTML = '<tr><td colspan="7" class="text-center text-muted">لا توجد غرامات مسجلة</td></tr>';
            return;
        }

        tbody.innerHTML = this.penalties.map(penalty => `
            <tr>
                <td>${this.getEmployeeName(penalty.employeeId)}</td>
                <td>${this.getPenaltyTypeText(penalty.type)}</td>
                <td>${this.formatCurrency(penalty.amount)}</td>
                <td>${this.formatDate(penalty.date)}</td>
                <td title="${penalty.reason}">${penalty.reason.length > 30 ? penalty.reason.substring(0, 30) + '...' : penalty.reason}</td>
                <td>${this.getStatusBadge(penalty.status, 'penalty')}</td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="financialManager.editPenalty('${penalty.id}')" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="financialManager.deleteItem('${penalty.id}', 'penalty')" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }

    renderLoansTable() {
        const tbody = document.getElementById('loansTable');
        if (!tbody) return;

        if (this.loans.length === 0) {
            tbody.innerHTML = '<tr><td colspan="8" class="text-center text-muted">لا توجد قروض مسجلة</td></tr>';
            return;
        }

        tbody.innerHTML = this.loans.map(loan => {
            const remaining = loan.amount - (loan.paidAmount || 0);
            return `
                <tr>
                    <td>${this.getEmployeeName(loan.employeeId)}</td>
                    <td>${this.formatCurrency(loan.amount)}</td>
                    <td>${this.formatCurrency(loan.paidAmount || 0)}</td>
                    <td>${this.formatCurrency(remaining)}</td>
                    <td>${this.formatCurrency(loan.monthlyInstallment)}</td>
                    <td>${this.formatDate(loan.date)}</td>
                    <td>${this.getStatusBadge(loan.status, 'loan')}</td>
                    <td>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary" onclick="financialManager.editLoan('${loan.id}')" title="تعديل">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-outline-info" onclick="financialManager.payLoanInstallment('${loan.id}')" title="دفع قسط">
                                <i class="fas fa-money-bill"></i>
                            </button>
                            <button class="btn btn-outline-danger" onclick="financialManager.deleteItem('${loan.id}', 'loan')" title="حذف">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        }).join('');
    }

    // Edit Functions
    editAllowance(id) {
        const allowance = this.allowances.find(a => a.id === id);
        if (!allowance) return;

        this.currentEditId = id;

        // Populate form
        document.getElementById('allowanceEmployee').value = allowance.employeeId;
        document.getElementById('allowanceType').value = allowance.type;
        document.getElementById('allowanceAmount').value = allowance.amount;
        document.getElementById('allowanceDate').value = allowance.date;
        document.getElementById('allowanceFrequency').value = allowance.frequency;
        document.getElementById('allowanceStatus').value = allowance.status;
        document.getElementById('allowanceNotes').value = allowance.notes || '';

        // Update modal title
        document.getElementById('allowanceModalTitle').textContent = 'تعديل العلاوة';

        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('allowanceModal'));
        modal.show();
    }

    editPenalty(id) {
        const penalty = this.penalties.find(p => p.id === id);
        if (!penalty) return;

        this.currentEditId = id;

        // Populate form
        document.getElementById('penaltyEmployee').value = penalty.employeeId;
        document.getElementById('penaltyType').value = penalty.type;
        document.getElementById('penaltyAmount').value = penalty.amount;
        document.getElementById('penaltyDate').value = penalty.date;
        document.getElementById('penaltyDeductionType').value = penalty.deductionType;
        document.getElementById('penaltyInstallments').value = penalty.installments;
        document.getElementById('penaltyReason').value = penalty.reason;
        document.getElementById('penaltyStatus').value = penalty.status;

        // Show/hide installments field
        const installmentsField = document.getElementById('installmentsField');
        if (penalty.deductionType === 'installments') {
            installmentsField.style.display = 'block';
        }

        // Update modal title
        document.getElementById('penaltyModalTitle').textContent = 'تعديل الغرامة';

        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('penaltyModal'));
        modal.show();
    }

    editLoan(id) {
        const loan = this.loans.find(l => l.id === id);
        if (!loan) return;

        this.currentEditId = id;

        // Populate form
        document.getElementById('loanEmployee').value = loan.employeeId;
        document.getElementById('loanType').value = loan.type;
        document.getElementById('loanAmount').value = loan.amount;
        document.getElementById('loanDate').value = loan.date;
        document.getElementById('loanDuration').value = loan.duration;
        document.getElementById('loanInterestRate').value = loan.interestRate;
        document.getElementById('monthlyInstallment').value = loan.monthlyInstallment;
        document.getElementById('loanPurpose').value = loan.purpose;
        document.getElementById('loanNotes').value = loan.notes || '';
        document.getElementById('loanStatus').value = loan.status;

        // Update modal title
        document.getElementById('loanModalTitle').textContent = 'تعديل القرض';

        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('loanModal'));
        modal.show();
    }

    // Delete Functions
    deleteItem(id, type) {
        this.currentEditId = id;
        this.currentEditType = type;

        const modal = new bootstrap.Modal(document.getElementById('deleteFinancialModal'));
        modal.show();
    }

    confirmDelete() {
        if (!this.currentEditId || !this.currentEditType) return;

        switch (this.currentEditType) {
            case 'allowance':
                this.allowances = this.allowances.filter(a => a.id !== this.currentEditId);
                this.saveToStorage('allowances', this.allowances);
                this.renderAllowancesTable();
                break;
            case 'penalty':
                this.penalties = this.penalties.filter(p => p.id !== this.currentEditId);
                this.saveToStorage('penalties', this.penalties);
                this.renderPenaltiesTable();
                break;
            case 'loan':
                this.loans = this.loans.filter(l => l.id !== this.currentEditId);
                this.saveToStorage('loans', this.loans);
                this.renderLoansTable();
                break;
        }

        this.updateSummaryCards();
        this.renderFinancialChart();

        const modal = bootstrap.Modal.getInstance(document.getElementById('deleteFinancialModal'));
        modal.hide();

        this.showAlert('تم الحذف بنجاح', 'success');
        this.currentEditId = null;
        this.currentEditType = null;
    }

    // Loan Payment Function
    payLoanInstallment(loanId) {
        const loan = this.loans.find(l => l.id === loanId);
        if (!loan) return;

        const remaining = loan.amount - (loan.paidAmount || 0);
        const installmentAmount = Math.min(loan.monthlyInstallment, remaining);

        if (remaining <= 0) {
            this.showAlert('هذا القرض مسدد بالكامل', 'info');
            return;
        }

        const confirmPayment = confirm(`هل تريد دفع قسط بمبلغ ${this.formatCurrency(installmentAmount)}؟`);
        if (!confirmPayment) return;

        loan.paidAmount = (loan.paidAmount || 0) + installmentAmount;

        // Check if loan is fully paid
        if (loan.paidAmount >= loan.amount) {
            loan.status = 'completed';
            loan.paidAmount = loan.amount; // Ensure exact amount
        }

        this.saveToStorage('loans', this.loans);
        this.renderLoansTable();
        this.updateSummaryCards();
        this.renderFinancialChart();

        this.showAlert(`تم دفع قسط بمبلغ ${this.formatCurrency(installmentAmount)}`, 'success');
    }

    // Summary and Statistics
    updateSummaryCards() {
        // Calculate totals
        const totalAllowances = this.allowances
            .filter(a => a.status === 'active')
            .reduce((sum, a) => sum + a.amount, 0);

        const totalPenalties = this.penalties
            .filter(p => p.status === 'active')
            .reduce((sum, p) => sum + p.amount, 0);

        const totalLoans = this.loans
            .filter(l => l.status === 'active')
            .reduce((sum, l) => sum + (l.amount - (l.paidAmount || 0)), 0);

        const netFinancialImpact = totalAllowances - totalPenalties - totalLoans;

        // Update cards
        document.getElementById('totalAllowances').textContent = this.formatCurrency(totalAllowances);
        document.getElementById('totalPenalties').textContent = this.formatCurrency(totalPenalties);
        document.getElementById('totalLoans').textContent = this.formatCurrency(totalLoans);
        document.getElementById('netFinancialImpact').textContent = this.formatCurrency(netFinancialImpact);

        // Update summary tab
        document.getElementById('activeAllowances').textContent = this.allowances.filter(a => a.status === 'active').length;
        document.getElementById('activePenalties').textContent = this.penalties.filter(p => p.status === 'active').length;
        document.getElementById('activeLoans').textContent = this.loans.filter(l => l.status === 'active').length;

        const totalMonthlyInstallments = this.loans
            .filter(l => l.status === 'active')
            .reduce((sum, l) => sum + l.monthlyInstallment, 0);
        document.getElementById('totalMonthlyInstallments').textContent = this.formatCurrency(totalMonthlyInstallments);
    }

    renderFinancialChart() {
        const ctx = document.getElementById('financialChart');
        if (!ctx) return;

        // Destroy existing chart
        if (this.financialChart) {
            this.financialChart.destroy();
        }

        const allowancesTotal = this.allowances.filter(a => a.status === 'active').reduce((sum, a) => sum + a.amount, 0);
        const penaltiesTotal = this.penalties.filter(p => p.status === 'active').reduce((sum, p) => sum + p.amount, 0);
        const loansTotal = this.loans.filter(l => l.status === 'active').reduce((sum, l) => sum + (l.amount - (l.paidAmount || 0)), 0);

        this.financialChart = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['العلاوات', 'الغرامات', 'القروض'],
                datasets: [{
                    data: [allowancesTotal, penaltiesTotal, loansTotal],
                    backgroundColor: [
                        '#28a745',
                        '#dc3545',
                        '#ffc107'
                    ],
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    },
                    tooltip: {
                        callbacks: {
                            label: (context) => {
                                const value = this.formatCurrency(context.parsed);
                                return `${context.label}: ${value}`;
                            }
                        }
                    }
                }
            }
        });
    }

    // Helper Functions
    getAllowanceTypeText(type) {
        const types = {
            performance: 'علاوة أداء',
            promotion: 'علاوة ترقية',
            overtime: 'علاوة ساعات إضافية',
            transport: 'بدل مواصلات',
            housing: 'بدل سكن',
            food: 'بدل طعام',
            special: 'علاوة خاصة',
            annual: 'علاوة سنوية'
        };
        return types[type] || type;
    }

    getPenaltyTypeText(type) {
        const types = {
            late: 'غرامة تأخير',
            absence: 'غرامة غياب',
            violation: 'مخالفة نظام',
            damage: 'تعويض أضرار',
            disciplinary: 'إجراء تأديبي',
            other: 'أخرى'
        };
        return types[type] || type;
    }

    getLoanTypeText(type) {
        const types = {
            personal: 'قرض شخصي',
            emergency: 'قرض طوارئ',
            housing: 'قرض سكن',
            car: 'قرض سيارة',
            education: 'قرض تعليم',
            medical: 'قرض طبي',
            advance: 'سلفة راتب'
        };
        return types[type] || type;
    }

    // Reset Forms
    resetAllowanceForm() {
        document.getElementById('allowanceForm').reset();
        document.getElementById('allowanceModalTitle').textContent = 'إضافة علاوة جديدة';
        const today = new Date().toISOString().split('T')[0];
        document.getElementById('allowanceDate').value = today;
    }

    resetPenaltyForm() {
        document.getElementById('penaltyForm').reset();
        document.getElementById('penaltyModalTitle').textContent = 'إضافة غرامة جديدة';
        document.getElementById('installmentsField').style.display = 'none';
        const today = new Date().toISOString().split('T')[0];
        document.getElementById('penaltyDate').value = today;
    }

    resetLoanForm() {
        document.getElementById('loanForm').reset();
        document.getElementById('loanModalTitle').textContent = 'إضافة قرض جديد';
        const today = new Date().toISOString().split('T')[0];
        document.getElementById('loanDate').value = today;
        document.getElementById('monthlyInstallment').value = '';
    }

    // Refresh Summary
    refreshSummary() {
        this.updateSummaryCards();
        this.renderFinancialChart();
        this.showAlert('تم تحديث الملخص المالي', 'success');
    }

    // Print Financial Report
    printFinancialReport() {
        const allowancesTotal = this.allowances.filter(a => a.status === 'active').reduce((sum, a) => sum + a.amount, 0);
        const penaltiesTotal = this.penalties.filter(p => p.status === 'active').reduce((sum, p) => sum + p.amount, 0);
        const loansTotal = this.loans.filter(l => l.status === 'active').reduce((sum, l) => sum + (l.amount - (l.paidAmount || 0)), 0);
        const netImpact = allowancesTotal - penaltiesTotal - loansTotal;

        const printContent = `
            <html>
            <head>
                <title>التقرير المالي الشامل</title>
                <style>
                    body {
                        font-family: Arial, sans-serif;
                        direction: rtl;
                        margin: 20px;
                        font-size: 12px;
                    }
                    .header {
                        text-align: center;
                        margin-bottom: 30px;
                        border-bottom: 2px solid #333;
                        padding-bottom: 20px;
                    }
                    .header h1 {
                        color: #333;
                        margin: 0;
                        font-size: 24px;
                    }
                    .summary-grid {
                        display: grid;
                        grid-template-columns: repeat(2, 1fr);
                        gap: 20px;
                        margin: 30px 0;
                    }
                    .summary-card {
                        background: #f8f9fa;
                        padding: 20px;
                        border-radius: 8px;
                        border: 1px solid #dee2e6;
                        text-align: center;
                    }
                    .summary-card h3 {
                        margin: 0 0 10px 0;
                        color: #333;
                    }
                    .summary-card .amount {
                        font-size: 20px;
                        font-weight: bold;
                    }
                    .positive { color: #28a745; }
                    .negative { color: #dc3545; }
                    .warning { color: #ffc107; }
                    table {
                        width: 100%;
                        border-collapse: collapse;
                        margin: 20px 0;
                        font-size: 11px;
                    }
                    th, td {
                        border: 1px solid #ddd;
                        padding: 8px;
                        text-align: center;
                    }
                    th {
                        background-color: #f8f9fa;
                        font-weight: bold;
                    }
                    .section-title {
                        background: #007bff;
                        color: white;
                        padding: 10px;
                        margin: 20px 0 10px 0;
                        border-radius: 5px;
                        text-align: center;
                        font-weight: bold;
                    }
                    @media print {
                        body { margin: 0; }
                    }
                </style>
            </head>
            <body>
                <div class="header">
                    <h1>التقرير المالي الشامل</h1>
                    <p>تاريخ التقرير: ${new Date().toLocaleDateString('ar-SA')}</p>
                    <p>وقت الإنشاء: ${new Date().toLocaleTimeString('ar-SA')}</p>
                </div>

                <div class="summary-grid">
                    <div class="summary-card">
                        <h3>إجمالي العلاوات</h3>
                        <div class="amount positive">${this.formatCurrency(allowancesTotal)}</div>
                    </div>
                    <div class="summary-card">
                        <h3>إجمالي الغرامات</h3>
                        <div class="amount negative">${this.formatCurrency(penaltiesTotal)}</div>
                    </div>
                    <div class="summary-card">
                        <h3>إجمالي القروض المتبقية</h3>
                        <div class="amount warning">${this.formatCurrency(loansTotal)}</div>
                    </div>
                    <div class="summary-card">
                        <h3>صافي التأثير المالي</h3>
                        <div class="amount ${netImpact >= 0 ? 'positive' : 'negative'}">${this.formatCurrency(netImpact)}</div>
                    </div>
                </div>

                <div class="section-title">العلاوات النشطة</div>
                <table>
                    <thead>
                        <tr>
                            <th>الموظف</th>
                            <th>نوع العلاوة</th>
                            <th>المبلغ</th>
                            <th>التاريخ</th>
                            <th>التكرار</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${this.allowances.filter(a => a.status === 'active').map(allowance => `
                            <tr>
                                <td>${this.getEmployeeName(allowance.employeeId)}</td>
                                <td>${this.getAllowanceTypeText(allowance.type)}</td>
                                <td>${this.formatCurrency(allowance.amount)}</td>
                                <td>${this.formatDate(allowance.date)}</td>
                                <td>${allowance.frequency}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>

                <div style="margin-top: 40px; text-align: center; font-size: 10px; color: #666;">
                    <p>تم إنشاء هذا التقرير بواسطة نظام إدارة الموظفين</p>
                    <p>جميع الحقوق محفوظة © ${new Date().getFullYear()}</p>
                </div>
            </body>
            </html>
        `;

        const printWindow = window.open('', '_blank');
        printWindow.document.write(printContent);
        printWindow.document.close();
        printWindow.print();
    }

    // Get financial data for payroll integration
    getEmployeeFinancialData(employeeId, month, year) {
        // Ensure parameters are properly converted
        const empId = employeeId.toString();
        const targetMonth = parseInt(month);
        const targetYear = parseInt(year);

        const monthlyAllowances = this.allowances
            .filter(a => a.employeeId.toString() === empId && a.status === 'active')
            .reduce((sum, a) => {
                // Calculate based on frequency
                switch (a.frequency) {
                    case 'monthly':
                        return sum + a.amount;
                    case 'quarterly':
                        return sum + (a.amount / 3);
                    case 'annual':
                        return sum + (a.amount / 12);
                    case 'one-time':
                        // Check if it's for this month
                        const allowanceDate = new Date(a.date);
                        if (allowanceDate.getMonth() + 1 === targetMonth && allowanceDate.getFullYear() === targetYear) {
                            return sum + a.amount;
                        }
                        return sum;
                    default:
                        return sum;
                }
            }, 0);

        const monthlyPenalties = this.penalties
            .filter(p => p.employeeId.toString() === empId && p.status === 'active')
            .reduce((sum, p) => {
                if (p.deductionType === 'immediate') {
                    // Check if it's for this month
                    const penaltyDate = new Date(p.date);
                    if (penaltyDate.getMonth() + 1 === targetMonth && penaltyDate.getFullYear() === targetYear) {
                        return sum + p.amount;
                    }
                    return sum;
                } else {
                    // Installments - check if penalty is still being paid
                    const penaltyDate = new Date(p.date);
                    const monthsSincePenalty = (targetYear - penaltyDate.getFullYear()) * 12 + (targetMonth - (penaltyDate.getMonth() + 1));

                    if (monthsSincePenalty >= 0 && monthsSincePenalty < p.installments) {
                        return sum + (p.amount / p.installments);
                    }
                    return sum;
                }
            }, 0);

        const monthlyLoanDeductions = this.loans
            .filter(l => l.employeeId.toString() === empId && l.status === 'active')
            .reduce((sum, l) => {
                // Check if loan is still active and not fully paid
                const remaining = l.amount - (l.paidAmount || 0);
                if (remaining > 0) {
                    return sum + Math.min(l.monthlyInstallment, remaining);
                }
                return sum;
            }, 0);

        return {
            allowances: monthlyAllowances,
            penalties: monthlyPenalties,
            loanDeductions: monthlyLoanDeductions,
            netFinancialImpact: monthlyAllowances - monthlyPenalties - monthlyLoanDeductions
        };
    }

    // Additional Financial Reports
    generateMonthlyAllowancesReport(month, year) {
        const targetMonth = parseInt(month);
        const targetYear = parseInt(year);

        const monthlyAllowances = this.allowances.filter(a => {
            if (a.status !== 'active') return false;

            switch (a.frequency) {
                case 'monthly':
                    return true;
                case 'quarterly':
                    return targetMonth % 3 === 1; // First month of quarter
                case 'annual':
                    return targetMonth === 1; // January
                case 'one-time':
                    const allowanceDate = new Date(a.date);
                    return allowanceDate.getMonth() + 1 === targetMonth && allowanceDate.getFullYear() === targetYear;
                default:
                    return false;
            }
        });

        const reportData = monthlyAllowances.map(allowance => ({
            employeeName: this.getEmployeeName(allowance.employeeId),
            type: this.getAllowanceTypeText(allowance.type),
            amount: allowance.amount,
            frequency: allowance.frequency,
            date: allowance.date,
            notes: allowance.notes || ''
        }));

        const totalAmount = reportData.reduce((sum, item) => sum + item.amount, 0);

        return {
            title: `تقرير العلاوات الشهرية - ${this.getMonthName(targetMonth)} ${targetYear}`,
            data: reportData,
            summary: {
                totalAllowances: reportData.length,
                totalAmount: totalAmount,
                averageAmount: reportData.length > 0 ? totalAmount / reportData.length : 0
            }
        };
    }

    generatePenaltiesReport(month, year) {
        const targetMonth = parseInt(month);
        const targetYear = parseInt(year);

        const monthlyPenalties = this.penalties.filter(p => {
            if (p.status !== 'active') return false;

            if (p.deductionType === 'immediate') {
                const penaltyDate = new Date(p.date);
                return penaltyDate.getMonth() + 1 === targetMonth && penaltyDate.getFullYear() === targetYear;
            } else {
                // Check if installment is due this month
                const penaltyDate = new Date(p.date);
                const monthsSincePenalty = (targetYear - penaltyDate.getFullYear()) * 12 + (targetMonth - (penaltyDate.getMonth() + 1));
                return monthsSincePenalty >= 0 && monthsSincePenalty < p.installments;
            }
        });

        const reportData = monthlyPenalties.map(penalty => ({
            employeeName: this.getEmployeeName(penalty.employeeId),
            type: this.getPenaltyTypeText(penalty.type),
            amount: penalty.deductionType === 'immediate' ? penalty.amount : penalty.amount / penalty.installments,
            reason: penalty.reason,
            date: penalty.date,
            deductionType: penalty.deductionType === 'immediate' ? 'خصم فوري' : 'أقساط شهرية'
        }));

        const totalAmount = reportData.reduce((sum, item) => sum + item.amount, 0);

        return {
            title: `تقرير الغرامات والخصومات - ${this.getMonthName(targetMonth)} ${targetYear}`,
            data: reportData,
            summary: {
                totalPenalties: reportData.length,
                totalAmount: totalAmount,
                averageAmount: reportData.length > 0 ? totalAmount / reportData.length : 0
            }
        };
    }

    generateLoansStatusReport() {
        const reportData = this.loans.map(loan => {
            const remaining = loan.amount - (loan.paidAmount || 0);
            const progressPercentage = ((loan.paidAmount || 0) / loan.amount) * 100;

            return {
                employeeName: this.getEmployeeName(loan.employeeId),
                type: this.getLoanTypeText(loan.type),
                originalAmount: loan.amount,
                paidAmount: loan.paidAmount || 0,
                remainingAmount: remaining,
                monthlyInstallment: loan.monthlyInstallment,
                progressPercentage: progressPercentage,
                status: loan.status,
                startDate: loan.date,
                estimatedEndDate: this.calculateLoanEndDate(loan)
            };
        });

        const activeLoans = reportData.filter(loan => loan.status === 'active');
        const completedLoans = reportData.filter(loan => loan.status === 'completed');
        const totalOriginalAmount = reportData.reduce((sum, loan) => sum + loan.originalAmount, 0);
        const totalPaidAmount = reportData.reduce((sum, loan) => sum + loan.paidAmount, 0);
        const totalRemainingAmount = reportData.reduce((sum, loan) => sum + loan.remainingAmount, 0);

        return {
            title: 'تقرير حالة القروض',
            data: reportData,
            summary: {
                totalLoans: reportData.length,
                activeLoans: activeLoans.length,
                completedLoans: completedLoans.length,
                totalOriginalAmount: totalOriginalAmount,
                totalPaidAmount: totalPaidAmount,
                totalRemainingAmount: totalRemainingAmount,
                overallProgress: totalOriginalAmount > 0 ? (totalPaidAmount / totalOriginalAmount) * 100 : 0
            }
        };
    }

    generateEmployeeFinancialImpactReport() {
        const employeeReports = this.employees.map(employee => {
            const currentMonth = new Date().getMonth() + 1;
            const currentYear = new Date().getFullYear();

            const financialData = this.getEmployeeFinancialData(employee.id, currentMonth, currentYear);

            return {
                employeeId: employee.id,
                employeeName: employee.name,
                department: employee.department,
                basicSalary: employee.salary,
                allowances: financialData.allowances,
                penalties: financialData.penalties,
                loanDeductions: financialData.loanDeductions,
                netFinancialImpact: financialData.netFinancialImpact,
                adjustedSalary: employee.salary + financialData.netFinancialImpact
            };
        });

        const totalBasicSalaries = employeeReports.reduce((sum, emp) => sum + emp.basicSalary, 0);
        const totalAllowances = employeeReports.reduce((sum, emp) => sum + emp.allowances, 0);
        const totalPenalties = employeeReports.reduce((sum, emp) => sum + emp.penalties, 0);
        const totalLoanDeductions = employeeReports.reduce((sum, emp) => sum + emp.loanDeductions, 0);
        const totalAdjustedSalaries = employeeReports.reduce((sum, emp) => sum + emp.adjustedSalary, 0);

        return {
            title: 'تقرير التأثير المالي الصافي للموظفين',
            data: employeeReports,
            summary: {
                totalEmployees: employeeReports.length,
                totalBasicSalaries: totalBasicSalaries,
                totalAllowances: totalAllowances,
                totalPenalties: totalPenalties,
                totalLoanDeductions: totalLoanDeductions,
                totalAdjustedSalaries: totalAdjustedSalaries,
                netFinancialImpact: totalAllowances - totalPenalties - totalLoanDeductions
            }
        };
    }

    // Helper functions for reports
    getMonthName(month) {
        const monthNames = ['', 'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
                           'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'];
        return monthNames[month] || month;
    }

    calculateLoanEndDate(loan) {
        if (loan.status === 'completed') return 'مكتمل';

        const startDate = new Date(loan.date);
        const remaining = loan.amount - (loan.paidAmount || 0);
        const remainingMonths = Math.ceil(remaining / loan.monthlyInstallment);

        const endDate = new Date(startDate);
        endDate.setMonth(endDate.getMonth() + remainingMonths);

        return endDate.toLocaleDateString('ar-SA');
    }

    // Print specific reports
    printMonthlyAllowancesReport(month, year) {
        const report = this.generateMonthlyAllowancesReport(month, year);
        this.printFinancialReport(report, 'allowances');
    }

    printPenaltiesReport(month, year) {
        const report = this.generatePenaltiesReport(month, year);
        this.printFinancialReport(report, 'penalties');
    }

    printLoansStatusReport() {
        const report = this.generateLoansStatusReport();
        this.printFinancialReport(report, 'loans');
    }

    printEmployeeFinancialImpactReport() {
        const report = this.generateEmployeeFinancialImpactReport();
        this.printFinancialReport(report, 'impact');
    }

    printFinancialReport(report, type) {
        let tableHeaders = '';
        let tableRows = '';

        switch (type) {
            case 'allowances':
                tableHeaders = `
                    <th>الموظف</th>
                    <th>نوع العلاوة</th>
                    <th>المبلغ</th>
                    <th>التكرار</th>
                    <th>التاريخ</th>
                    <th>ملاحظات</th>
                `;
                tableRows = report.data.map(item => `
                    <tr>
                        <td>${item.employeeName}</td>
                        <td>${item.type}</td>
                        <td>${this.formatCurrency(item.amount)}</td>
                        <td>${item.frequency}</td>
                        <td>${this.formatDate(item.date)}</td>
                        <td>${item.notes}</td>
                    </tr>
                `).join('');
                break;

            case 'penalties':
                tableHeaders = `
                    <th>الموظف</th>
                    <th>نوع الغرامة</th>
                    <th>المبلغ</th>
                    <th>السبب</th>
                    <th>التاريخ</th>
                    <th>طريقة الخصم</th>
                `;
                tableRows = report.data.map(item => `
                    <tr>
                        <td>${item.employeeName}</td>
                        <td>${item.type}</td>
                        <td>${this.formatCurrency(item.amount)}</td>
                        <td>${item.reason}</td>
                        <td>${this.formatDate(item.date)}</td>
                        <td>${item.deductionType}</td>
                    </tr>
                `).join('');
                break;

            case 'loans':
                tableHeaders = `
                    <th>الموظف</th>
                    <th>نوع القرض</th>
                    <th>المبلغ الأصلي</th>
                    <th>المبلغ المسدد</th>
                    <th>المتبقي</th>
                    <th>القسط الشهري</th>
                    <th>نسبة الإنجاز</th>
                    <th>الحالة</th>
                `;
                tableRows = report.data.map(item => `
                    <tr>
                        <td>${item.employeeName}</td>
                        <td>${item.type}</td>
                        <td>${this.formatCurrency(item.originalAmount)}</td>
                        <td>${this.formatCurrency(item.paidAmount)}</td>
                        <td>${this.formatCurrency(item.remainingAmount)}</td>
                        <td>${this.formatCurrency(item.monthlyInstallment)}</td>
                        <td>${item.progressPercentage.toFixed(1)}%</td>
                        <td>${item.status}</td>
                    </tr>
                `).join('');
                break;

            case 'impact':
                tableHeaders = `
                    <th>الموظف</th>
                    <th>القسم</th>
                    <th>الراتب الأساسي</th>
                    <th>العلاوات</th>
                    <th>الغرامات</th>
                    <th>خصم القروض</th>
                    <th>التأثير الصافي</th>
                    <th>الراتب المعدل</th>
                `;
                tableRows = report.data.map(item => `
                    <tr>
                        <td>${item.employeeName}</td>
                        <td>${item.department}</td>
                        <td>${this.formatCurrency(item.basicSalary)}</td>
                        <td class="text-success">${this.formatCurrency(item.allowances)}</td>
                        <td class="text-danger">${this.formatCurrency(item.penalties)}</td>
                        <td class="text-warning">${this.formatCurrency(item.loanDeductions)}</td>
                        <td class="${item.netFinancialImpact >= 0 ? 'text-success' : 'text-danger'}">${this.formatCurrency(item.netFinancialImpact)}</td>
                        <td><strong>${this.formatCurrency(item.adjustedSalary)}</strong></td>
                    </tr>
                `).join('');
                break;
        }

        const printContent = `
            <html>
            <head>
                <title>${report.title}</title>
                <style>
                    body {
                        font-family: Arial, sans-serif;
                        direction: rtl;
                        margin: 20px;
                        font-size: 12px;
                    }
                    .header {
                        text-align: center;
                        margin-bottom: 30px;
                        border-bottom: 2px solid #333;
                        padding-bottom: 20px;
                    }
                    .header h1 {
                        color: #333;
                        margin: 0;
                        font-size: 24px;
                    }
                    table {
                        width: 100%;
                        border-collapse: collapse;
                        margin: 20px 0;
                        font-size: 11px;
                    }
                    th, td {
                        border: 1px solid #ddd;
                        padding: 8px;
                        text-align: center;
                    }
                    th {
                        background-color: #f8f9fa;
                        font-weight: bold;
                    }
                    .text-success { color: #28a745; }
                    .text-danger { color: #dc3545; }
                    .text-warning { color: #ffc107; }
                    .summary {
                        background: #f8f9fa;
                        padding: 20px;
                        border-radius: 8px;
                        margin: 20px 0;
                    }
                    @media print {
                        body { margin: 0; }
                    }
                </style>
            </head>
            <body>
                <div class="header">
                    <h1>${report.title}</h1>
                    <p>تاريخ التقرير: ${new Date().toLocaleDateString('ar-SA')}</p>
                </div>

                <table>
                    <thead>
                        <tr>${tableHeaders}</tr>
                    </thead>
                    <tbody>
                        ${tableRows}
                    </tbody>
                </table>

                <div class="summary">
                    <h3>ملخص التقرير</h3>
                    ${this.generateReportSummaryHTML(report.summary, type)}
                </div>

                <div style="margin-top: 40px; text-align: center; font-size: 10px; color: #666;">
                    <p>تم إنشاء هذا التقرير بواسطة نظام إدارة الموظفين</p>
                    <p>جميع الحقوق محفوظة © ${new Date().getFullYear()}</p>
                </div>
            </body>
            </html>
        `;

        const printWindow = window.open('', '_blank');
        printWindow.document.write(printContent);
        printWindow.document.close();
        printWindow.print();
    }

    generateReportSummaryHTML(summary, type) {
        switch (type) {
            case 'allowances':
                return `
                    <p><strong>إجمالي العلاوات:</strong> ${summary.totalAllowances}</p>
                    <p><strong>إجمالي المبلغ:</strong> ${this.formatCurrency(summary.totalAmount)}</p>
                    <p><strong>متوسط العلاوة:</strong> ${this.formatCurrency(summary.averageAmount)}</p>
                `;
            case 'penalties':
                return `
                    <p><strong>إجمالي الغرامات:</strong> ${summary.totalPenalties}</p>
                    <p><strong>إجمالي المبلغ:</strong> ${this.formatCurrency(summary.totalAmount)}</p>
                    <p><strong>متوسط الغرامة:</strong> ${this.formatCurrency(summary.averageAmount)}</p>
                `;
            case 'loans':
                return `
                    <p><strong>إجمالي القروض:</strong> ${summary.totalLoans}</p>
                    <p><strong>القروض النشطة:</strong> ${summary.activeLoans}</p>
                    <p><strong>القروض المكتملة:</strong> ${summary.completedLoans}</p>
                    <p><strong>إجمالي المبلغ الأصلي:</strong> ${this.formatCurrency(summary.totalOriginalAmount)}</p>
                    <p><strong>إجمالي المبلغ المسدد:</strong> ${this.formatCurrency(summary.totalPaidAmount)}</p>
                    <p><strong>إجمالي المتبقي:</strong> ${this.formatCurrency(summary.totalRemainingAmount)}</p>
                    <p><strong>نسبة الإنجاز الإجمالية:</strong> ${summary.overallProgress.toFixed(1)}%</p>
                `;
            case 'impact':
                return `
                    <p><strong>إجمالي الموظفين:</strong> ${summary.totalEmployees}</p>
                    <p><strong>إجمالي الرواتب الأساسية:</strong> ${this.formatCurrency(summary.totalBasicSalaries)}</p>
                    <p><strong>إجمالي العلاوات:</strong> ${this.formatCurrency(summary.totalAllowances)}</p>
                    <p><strong>إجمالي الغرامات:</strong> ${this.formatCurrency(summary.totalPenalties)}</p>
                    <p><strong>إجمالي خصم القروض:</strong> ${this.formatCurrency(summary.totalLoanDeductions)}</p>
                    <p><strong>التأثير المالي الصافي:</strong> ${this.formatCurrency(summary.netFinancialImpact)}</p>
                    <p><strong>إجمالي الرواتب المعدلة:</strong> ${this.formatCurrency(summary.totalAdjustedSalaries)}</p>
                `;
            default:
                return '';
        }
    }
}

// Global instance
const financialManager = new FinancialManager();

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', async function() {
    console.log('DOM loaded, initializing financial manager...');
    await financialManager.init();
    console.log('Financial manager initialized successfully');
});

// Refresh data when page becomes visible
document.addEventListener('visibilitychange', function() {
    if (!document.hidden) {
        console.log('Page became visible, refreshing financial data...');
        financialManager.populateEmployeeDropdowns();
    }
});
