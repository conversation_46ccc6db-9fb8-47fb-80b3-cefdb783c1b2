<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الحضور والانصراف - نظام إدارة الموظفين</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="styles.css" rel="stylesheet">
</head>
<body>
    <!-- Sidebar -->
    <nav class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <h4 class="text-white mb-0">
                <i class="fas fa-users-cog me-2"></i>
                <span class="sidebar-text">نظام الموظفين</span>
            </h4>
        </div>
        <ul class="sidebar-menu">
            <li>
                <a href="dashboard.html">
                    <i class="fas fa-tachometer-alt"></i>
                    <span class="sidebar-text">لوحة التحكم</span>
                </a>
            </li>
            <li>
                <a href="employees.html">
                    <i class="fas fa-users"></i>
                    <span class="sidebar-text">إدارة الموظفين</span>
                </a>
            </li>
            <li>
                <a href="payroll.html">
                    <i class="fas fa-calculator"></i>
                    <span class="sidebar-text">حساب الرواتب</span>
                </a>
            </li>
            <li>
                <a href="financial-management.html">
                    <i class="fas fa-coins"></i>
                    <span class="sidebar-text">الإدارة المالية</span>
                </a>
            </li>
            <li>
                <a href="leaves.html">
                    <i class="fas fa-calendar-alt"></i>
                    <span class="sidebar-text">إدارة الإجازات</span>
                </a>
            </li>
            <li>
                <a href="reports.html">
                    <i class="fas fa-chart-bar"></i>
                    <span class="sidebar-text">التقارير</span>
                </a>
            </li>
            <li>
                <a href="attendance.html" class="active">
                    <i class="fas fa-clock"></i>
                    <span class="sidebar-text">الحضور والانصراف</span>
                </a>
            </li>
            <li>
                <a href="settings.html">
                    <i class="fas fa-cog"></i>
                    <span class="sidebar-text">الإعدادات</span>
                </a>
            </li>
            <li>
                <a href="#" class="logout-btn">
                    <i class="fas fa-sign-out-alt"></i>
                    <span class="sidebar-text">تسجيل الخروج</span>
                </a>
            </li>
        </ul>
    </nav>

    <!-- Main Content -->
    <div class="main-content" id="mainContent">
        <!-- Header -->
        <header class="header">
            <div class="d-flex align-items-center">
                <button class="btn btn-link text-dark me-3" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h3 class="header-title">الحضور والانصراف</h3>
            </div>
            <div class="user-info">
                <span class="user-name me-2"></span>
                <span class="user-role badge bg-primary me-2"></span>
                <div class="user-avatar"></div>
            </div>
        </header>

        <!-- Content Area -->
        <div class="content-area">
            <!-- Quick Actions -->
            <div class="row mb-4">
                <div class="col-lg-6 mb-3">
                    <div class="form-container">
                        <h5 class="mb-3">تسجيل الحضور السريع</h5>
                        <div class="row">
                            <div class="col-md-8 mb-3">
                                <label for="employeeSelect" class="form-label">اختر الموظف</label>
                                <select class="form-select" id="employeeSelect">
                                    <option value="">اختر الموظف</option>
                                </select>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button class="btn btn-success" id="markAttendanceBtn">
                                        <i class="fas fa-check me-1"></i>
                                        تسجيل حضور
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6 mb-3">
                    <div class="form-container">
                        <h5 class="mb-3">الوقت الحالي</h5>
                        <div class="text-center">
                            <div class="display-6" id="currentTime">00:00:00</div>
                            <div class="text-muted" id="currentDate">اليوم</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Attendance Filter -->
            <div class="form-container mb-4">
                <h5 class="mb-3">تصفية سجلات الحضور</h5>
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label for="filterDate" class="form-label">التاريخ</label>
                        <input type="date" class="form-control" id="filterDate">
                    </div>
                    <div class="col-md-3 mb-3">
                        <label for="filterDepartment" class="form-label">القسم</label>
                        <select class="form-select" id="filterDepartment">
                            <option value="">جميع الأقسام</option>
                        </select>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label for="filterStatus" class="form-label">حالة الحضور</label>
                        <select class="form-select" id="filterStatus">
                            <option value="">جميع الحالات</option>
                            <option value="present">حاضر</option>
                            <option value="absent">غائب</option>
                            <option value="late">متأخر</option>
                        </select>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button class="btn btn-primary" id="filterAttendanceBtn">
                                <i class="fas fa-filter me-1"></i>
                                تطبيق التصفية
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Attendance Statistics -->
            <div class="row mb-4">
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="stat-card success">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="stat-number" id="presentCount">0</h3>
                                <p class="stat-label">حاضر اليوم</p>
                            </div>
                            <i class="fas fa-user-check fa-2x text-success"></i>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="stat-card danger">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="stat-number" id="absentCount">0</h3>
                                <p class="stat-label">غائب اليوم</p>
                            </div>
                            <i class="fas fa-user-times fa-2x text-danger"></i>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="stat-card warning">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="stat-number" id="lateCount">0</h3>
                                <p class="stat-label">متأخر اليوم</p>
                            </div>
                            <i class="fas fa-clock fa-2x text-warning"></i>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="stat-card info">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="stat-number" id="attendanceRate">0%</h3>
                                <p class="stat-label">معدل الحضور</p>
                            </div>
                            <i class="fas fa-percentage fa-2x text-info"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Attendance Table -->
            <div class="table-container">
                <div class="table-header">
                    <h5 class="mb-0">سجلات الحضور</h5>
                    <div>
                        <button class="btn btn-success me-2" id="addAttendanceBtn">
                            <i class="fas fa-plus me-1"></i>
                            إضافة سجل
                        </button>
                        <button class="btn btn-info" id="exportAttendanceBtn">
                            <i class="fas fa-download me-1"></i>
                            تصدير
                        </button>
                    </div>
                </div>
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>التاريخ</th>
                                <th>الموظف</th>
                                <th>القسم</th>
                                <th>وقت الحضور</th>
                                <th>وقت الانصراف</th>
                                <th>ساعات العمل</th>
                                <th>الحالة</th>
                                <th>ملاحظات</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="attendanceTableBody">
                            <tr>
                                <td colspan="9" class="text-center text-muted">جاري تحميل البيانات...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Add/Edit Attendance Modal -->
    <div class="modal fade" id="attendanceModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="attendanceModalTitle">إضافة سجل حضور</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="attendanceForm" class="needs-validation" novalidate>
                        <input type="hidden" id="attendanceId">
                        
                        <div class="mb-3">
                            <label for="attendanceEmployee" class="form-label">الموظف *</label>
                            <select class="form-select" id="attendanceEmployee" required>
                                <option value="">اختر الموظف</option>
                            </select>
                            <div class="invalid-feedback">يرجى اختيار الموظف</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="attendanceDate" class="form-label">التاريخ *</label>
                            <input type="date" class="form-control" id="attendanceDate" required>
                            <div class="invalid-feedback">يرجى إدخال التاريخ</div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="checkInTime" class="form-label">وقت الحضور</label>
                                <input type="time" class="form-control" id="checkInTime">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="checkOutTime" class="form-label">وقت الانصراف</label>
                                <input type="time" class="form-control" id="checkOutTime">
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="attendanceStatus" class="form-label">الحالة *</label>
                            <select class="form-select" id="attendanceStatus" required>
                                <option value="">اختر الحالة</option>
                                <option value="present">حاضر</option>
                                <option value="absent">غائب</option>
                                <option value="late">متأخر</option>
                                <option value="half-day">نصف يوم</option>
                            </select>
                            <div class="invalid-feedback">يرجى اختيار الحالة</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="attendanceNotes" class="form-label">ملاحظات</label>
                            <textarea class="form-control" id="attendanceNotes" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" form="attendanceForm" class="btn btn-primary" id="saveAttendanceBtn">
                        <i class="fas fa-save me-1"></i>
                        حفظ
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="database.js"></script>
    <script src="auth.js"></script>
    <script>
        // Simple attendance management
        class AttendanceManager {
            constructor() {
                this.employees = [];
                this.departments = [];
                this.attendanceRecords = [];
            }

            async init() {
                await this.loadData();
                this.setupEventListeners();
                this.updateClock();
                this.setupSidebar();
                this.loadTodayStats();
            }

            async loadData() {
                try {
                    this.employees = await dbManager.getAll('employees');
                    this.departments = await dbManager.getAll('departments');
                    this.attendanceRecords = await dbManager.getAll('attendance') || [];
                    
                    this.populateDropdowns();
                } catch (error) {
                    console.error('Error loading data:', error);
                }
            }

            populateDropdowns() {
                const employeeSelect = document.getElementById('employeeSelect');
                const attendanceEmployee = document.getElementById('attendanceEmployee');
                const filterDepartment = document.getElementById('filterDepartment');

                // Populate employee selects
                [employeeSelect, attendanceEmployee].forEach(select => {
                    select.innerHTML = '<option value="">اختر الموظف</option>';
                    this.employees.filter(emp => emp.status === 'active').forEach(emp => {
                        select.innerHTML += `<option value="${emp.id}">${emp.name} - ${emp.employeeNumber}</option>`;
                    });
                });

                // Populate department filter
                filterDepartment.innerHTML = '<option value="">جميع الأقسام</option>';
                this.departments.forEach(dept => {
                    filterDepartment.innerHTML += `<option value="${dept.name}">${dept.name}</option>`;
                });
            }

            setupEventListeners() {
                // Mark attendance button
                document.getElementById('markAttendanceBtn').addEventListener('click', () => this.markAttendance());

                // Add attendance button
                document.getElementById('addAttendanceBtn').addEventListener('click', () => this.showAttendanceModal());

                // Filter button
                document.getElementById('filterAttendanceBtn').addEventListener('click', () => this.filterAttendance());

                // Attendance form
                document.getElementById('attendanceForm').addEventListener('submit', (e) => this.saveAttendance(e));

                // Set today's date as default
                document.getElementById('filterDate').value = new Date().toISOString().split('T')[0];
                document.getElementById('attendanceDate').value = new Date().toISOString().split('T')[0];
            }

            setupSidebar() {
                const sidebarToggle = document.getElementById('sidebarToggle');
                const sidebar = document.getElementById('sidebar');
                const mainContent = document.getElementById('mainContent');

                if (sidebarToggle) {
                    sidebarToggle.addEventListener('click', () => {
                        sidebar.classList.toggle('collapsed');
                        mainContent.classList.toggle('expanded');
                    });
                }
            }

            updateClock() {
                const now = new Date();
                document.getElementById('currentTime').textContent = now.toLocaleTimeString('ar-SA');
                document.getElementById('currentDate').textContent = now.toLocaleDateString('ar-SA', {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                });

                setTimeout(() => this.updateClock(), 1000);
            }

            async markAttendance() {
                const employeeId = document.getElementById('employeeSelect').value;
                if (!employeeId) {
                    alert('يرجى اختيار الموظف');
                    return;
                }

                const today = new Date().toISOString().split('T')[0];
                const now = new Date().toTimeString().split(' ')[0];

                // Check if already marked today
                const existingRecord = this.attendanceRecords.find(record => 
                    record.employeeId == employeeId && record.date === today
                );

                if (existingRecord) {
                    alert('تم تسجيل حضور هذا الموظف اليوم');
                    return;
                }

                const employee = this.employees.find(emp => emp.id == employeeId);
                const attendanceRecord = {
                    employeeId: parseInt(employeeId),
                    employeeName: employee.name,
                    department: employee.department,
                    date: today,
                    checkInTime: now,
                    status: 'present',
                    createdAt: new Date().toISOString()
                };

                try {
                    await dbManager.add('attendance', attendanceRecord);
                    this.attendanceRecords.push(attendanceRecord);
                    this.loadTodayStats();
                    alert('تم تسجيل الحضور بنجاح');
                    document.getElementById('employeeSelect').value = '';
                } catch (error) {
                    console.error('Error marking attendance:', error);
                    alert('حدث خطأ في تسجيل الحضور');
                }
            }

            loadTodayStats() {
                const today = new Date().toISOString().split('T')[0];
                const todayRecords = this.attendanceRecords.filter(record => record.date === today);
                
                const presentCount = todayRecords.filter(record => record.status === 'present').length;
                const lateCount = todayRecords.filter(record => record.status === 'late').length;
                const totalActiveEmployees = this.employees.filter(emp => emp.status === 'active').length;
                const absentCount = totalActiveEmployees - presentCount - lateCount;
                const attendanceRate = totalActiveEmployees > 0 ? Math.round((presentCount + lateCount) / totalActiveEmployees * 100) : 0;

                document.getElementById('presentCount').textContent = presentCount;
                document.getElementById('absentCount').textContent = absentCount;
                document.getElementById('lateCount').textContent = lateCount;
                document.getElementById('attendanceRate').textContent = attendanceRate + '%';
            }

            showAttendanceModal() {
                const modal = new bootstrap.Modal(document.getElementById('attendanceModal'));
                document.getElementById('attendanceForm').reset();
                document.getElementById('attendanceDate').value = new Date().toISOString().split('T')[0];
                modal.show();
            }

            async saveAttendance(e) {
                e.preventDefault();
                
                const form = e.target;
                if (!form.checkValidity()) {
                    e.stopPropagation();
                    form.classList.add('was-validated');
                    return;
                }

                const employeeId = parseInt(document.getElementById('attendanceEmployee').value);
                const employee = this.employees.find(emp => emp.id === employeeId);
                
                const attendanceData = {
                    employeeId,
                    employeeName: employee.name,
                    department: employee.department,
                    date: document.getElementById('attendanceDate').value,
                    checkInTime: document.getElementById('checkInTime').value,
                    checkOutTime: document.getElementById('checkOutTime').value,
                    status: document.getElementById('attendanceStatus').value,
                    notes: document.getElementById('attendanceNotes').value,
                    createdAt: new Date().toISOString()
                };

                try {
                    await dbManager.add('attendance', attendanceData);
                    this.attendanceRecords.push(attendanceData);
                    this.loadTodayStats();
                    bootstrap.Modal.getInstance(document.getElementById('attendanceModal')).hide();
                    alert('تم حفظ سجل الحضور بنجاح');
                } catch (error) {
                    console.error('Error saving attendance:', error);
                    alert('حدث خطأ في حفظ السجل');
                }
            }

            filterAttendance() {
                // This would filter and display attendance records
                alert('ميزة التصفية قيد التطوير');
            }
        }

        // Initialize attendance manager
        const attendanceManager = new AttendanceManager();

        document.addEventListener('DOMContentLoaded', async function() {
            await initializeDatabase();
            await attendanceManager.init();
        });
    </script>
</body>
</html>
