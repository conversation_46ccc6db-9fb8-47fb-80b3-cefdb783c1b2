<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الإجازات - نظام إدارة الموظفين</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link href="styles.css" rel="stylesheet">
</head>
<body>
    <!-- Sidebar -->
    <nav class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <div class="sidebar-brand">
                <i class="fas fa-building"></i>
                <span class="sidebar-text">إدارة الموظفين</span>
            </div>
        </div>
        <ul class="sidebar-nav">
            <li>
                <a href="dashboard.html">
                    <i class="fas fa-tachometer-alt"></i>
                    <span class="sidebar-text">لوحة التحكم</span>
                </a>
            </li>
            <li>
                <a href="employees.html">
                    <i class="fas fa-users"></i>
                    <span class="sidebar-text">إدارة الموظفين</span>
                </a>
            </li>
            <li>
                <a href="payroll.html">
                    <i class="fas fa-calculator"></i>
                    <span class="sidebar-text">حساب الرواتب</span>
                </a>
            </li>
            <li>
                <a href="financial-management.html">
                    <i class="fas fa-coins"></i>
                    <span class="sidebar-text">الإدارة المالية</span>
                </a>
            </li>
            <li class="active">
                <a href="leaves.html">
                    <i class="fas fa-calendar-alt"></i>
                    <span class="sidebar-text">إدارة الإجازات</span>
                </a>
            </li>
            <li>
                <a href="reports.html">
                    <i class="fas fa-chart-bar"></i>
                    <span class="sidebar-text">التقارير</span>
                </a>
            </li>
            <li>
                <a href="attendance.html">
                    <i class="fas fa-clock"></i>
                    <span class="sidebar-text">الحضور والانصراف</span>
                </a>
            </li>
            <li>
                <a href="settings.html">
                    <i class="fas fa-cog"></i>
                    <span class="sidebar-text">الإعدادات</span>
                </a>
            </li>
            <li>
                <a href="#" class="logout-btn">
                    <i class="fas fa-sign-out-alt"></i>
                    <span class="sidebar-text">تسجيل الخروج</span>
                </a>
            </li>
        </ul>
    </nav>

    <!-- Main Content -->
    <div class="main-content" id="mainContent">
        <!-- Header -->
        <header class="header">
            <div class="d-flex align-items-center">
                <button class="btn btn-link text-dark me-3" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h3 class="header-title">إدارة الإجازات</h3>
            </div>
            <div class="d-flex align-items-center">
                <button class="btn btn-outline-primary me-3" onclick="leavesManager.printLeavesReport()" title="طباعة تقرير الإجازات">
                    <i class="fas fa-print me-1"></i>
                    طباعة
                </button>
                <div class="user-info">
                    <span class="user-name me-2"></span>
                    <span class="user-role badge bg-primary me-2"></span>
                    <div class="user-avatar"></div>
                </div>
            </div>
        </header>

        <!-- Content Area -->
        <div class="content-area">
            <!-- Leave Summary Cards -->
            <div class="row mb-4">
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="stat-card success fade-in">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="stat-number" id="totalApprovedLeaves">0</h3>
                                <p class="stat-label">الإجازات المعتمدة</p>
                                <div class="progress mt-2" style="height: 4px;">
                                    <div class="progress-bar bg-success" role="progressbar" style="width: 75%"></div>
                                </div>
                            </div>
                            <div class="stat-icon">
                                <i class="fas fa-check-circle fa-2x text-success"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="stat-card warning slide-in-right">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="stat-number" id="totalPendingLeaves">0</h3>
                                <p class="stat-label">في انتظار الموافقة</p>
                                <div class="progress mt-2" style="height: 4px;">
                                    <div class="progress-bar bg-warning" role="progressbar" style="width: 60%"></div>
                                </div>
                            </div>
                            <div class="stat-icon">
                                <i class="fas fa-clock fa-2x text-warning"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="stat-card danger bounce-in">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="stat-number" id="totalRejectedLeaves">0</h3>
                                <p class="stat-label">الإجازات المرفوضة</p>
                                <div class="progress mt-2" style="height: 4px;">
                                    <div class="progress-bar bg-danger" role="progressbar" style="width: 30%"></div>
                                </div>
                            </div>
                            <div class="stat-icon">
                                <i class="fas fa-times-circle fa-2x text-danger"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="stat-card info slide-in-left">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="stat-number" id="totalLeaveDays">0</h3>
                                <p class="stat-label">إجمالي أيام الإجازات</p>
                                <div class="progress mt-2" style="height: 4px;">
                                    <div class="progress-bar bg-info" role="progressbar" style="width: 85%"></div>
                                </div>
                            </div>
                            <div class="stat-icon">
                                <i class="fas fa-calendar-day fa-2x text-info"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Leave Management Tabs -->
            <div class="row">
                <div class="col-12">
                    <div class="table-container">
                        <div class="table-header">
                            <h5 class="mb-0">إدارة الإجازات</h5>
                        </div>
                        
                        <!-- Navigation Tabs -->
                        <ul class="nav nav-tabs leave-tabs" id="leaveTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="all-leaves-tab" data-bs-toggle="tab" data-bs-target="#all-leaves" type="button" role="tab">
                                    <i class="fas fa-list me-2"></i>جميع الإجازات
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="pending-leaves-tab" data-bs-toggle="tab" data-bs-target="#pending-leaves" type="button" role="tab">
                                    <i class="fas fa-hourglass-half me-2"></i>في الانتظار
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="leave-balance-tab" data-bs-toggle="tab" data-bs-target="#leave-balance" type="button" role="tab">
                                    <i class="fas fa-balance-scale me-2"></i>رصيد الإجازات
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="leave-calendar-tab" data-bs-toggle="tab" data-bs-target="#leave-calendar" type="button" role="tab">
                                    <i class="fas fa-calendar me-2"></i>التقويم
                                </button>
                            </li>
                        </ul>

                        <!-- Tab Content -->
                        <div class="tab-content leave-tab-content" id="leaveTabContent">
                            <!-- All Leaves Tab -->
                            <div class="tab-pane fade show active" id="all-leaves" role="tabpanel">
                                <div class="leave-section">
                                    <div class="section-header">
                                        <h6>جميع طلبات الإجازات</h6>
                                        <div class="d-flex gap-2">
                                            <select class="form-select form-select-sm" id="filterStatus" style="width: auto;">
                                                <option value="">جميع الحالات</option>
                                                <option value="pending">في الانتظار</option>
                                                <option value="approved">معتمدة</option>
                                                <option value="rejected">مرفوضة</option>
                                                <option value="cancelled">ملغية</option>
                                            </select>
                                            <select class="form-select form-select-sm" id="filterType" style="width: auto;">
                                                <option value="">جميع الأنواع</option>
                                                <option value="annual">إجازة سنوية</option>
                                                <option value="sick">إجازة مرضية</option>
                                                <option value="emergency">إجازة طارئة</option>
                                                <option value="maternity">إجازة أمومة</option>
                                                <option value="paternity">إجازة أبوة</option>
                                                <option value="unpaid">إجازة بدون راتب</option>
                                            </select>
                                            <button class="btn btn-success btn-sm" data-bs-toggle="modal" data-bs-target="#leaveModal">
                                                <i class="fas fa-plus me-1"></i>طلب إجازة
                                            </button>
                                        </div>
                                    </div>
                                    <div class="table-responsive">
                                        <table class="table table-hover">
                                            <thead>
                                                <tr>
                                                    <th>الموظف</th>
                                                    <th>نوع الإجازة</th>
                                                    <th>تاريخ البداية</th>
                                                    <th>تاريخ النهاية</th>
                                                    <th>عدد الأيام</th>
                                                    <th>السبب</th>
                                                    <th>الحالة</th>
                                                    <th>تاريخ الطلب</th>
                                                    <th>الإجراءات</th>
                                                </tr>
                                            </thead>
                                            <tbody id="allLeavesTable">
                                                <tr>
                                                    <td colspan="9" class="text-center text-muted">لا توجد طلبات إجازات</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>

                            <!-- Pending Leaves Tab -->
                            <div class="tab-pane fade" id="pending-leaves" role="tabpanel">
                                <div class="leave-section">
                                    <div class="section-header">
                                        <h6>الطلبات في انتظار الموافقة</h6>
                                        <button class="btn btn-info btn-sm" onclick="leavesManager.approveAllPending()">
                                            <i class="fas fa-check-double me-1"></i>اعتماد الكل
                                        </button>
                                    </div>
                                    <div class="table-responsive">
                                        <table class="table table-hover">
                                            <thead>
                                                <tr>
                                                    <th>الموظف</th>
                                                    <th>نوع الإجازة</th>
                                                    <th>الفترة</th>
                                                    <th>عدد الأيام</th>
                                                    <th>السبب</th>
                                                    <th>تاريخ الطلب</th>
                                                    <th>الإجراءات</th>
                                                </tr>
                                            </thead>
                                            <tbody id="pendingLeavesTable">
                                                <tr>
                                                    <td colspan="7" class="text-center text-muted">لا توجد طلبات في الانتظار</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>

                            <!-- Leave Balance Tab -->
                            <div class="tab-pane fade" id="leave-balance" role="tabpanel">
                                <div class="leave-section">
                                    <div class="section-header">
                                        <h6>رصيد الإجازات للموظفين</h6>
                                        <button class="btn btn-primary btn-sm" onclick="leavesManager.updateAllBalances()">
                                            <i class="fas fa-sync-alt me-1"></i>تحديث الأرصدة
                                        </button>
                                    </div>
                                    <div class="table-responsive">
                                        <table class="table table-hover">
                                            <thead>
                                                <tr>
                                                    <th>الموظف</th>
                                                    <th>القسم</th>
                                                    <th>الإجازة السنوية</th>
                                                    <th>المستخدم</th>
                                                    <th>المتبقي</th>
                                                    <th>الإجازة المرضية</th>
                                                    <th>المستخدم</th>
                                                    <th>المتبقي</th>
                                                    <th>الإجراءات</th>
                                                </tr>
                                            </thead>
                                            <tbody id="leaveBalanceTable">
                                                <tr>
                                                    <td colspan="9" class="text-center text-muted">لا توجد بيانات أرصدة</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>

                            <!-- Leave Calendar Tab -->
                            <div class="tab-pane fade" id="leave-calendar" role="tabpanel">
                                <div class="leave-section">
                                    <div class="section-header">
                                        <h6>تقويم الإجازات</h6>
                                        <div class="d-flex gap-2">
                                            <select class="form-select form-select-sm" id="calendarMonth" style="width: auto;">
                                                <option value="1">يناير</option>
                                                <option value="2">فبراير</option>
                                                <option value="3">مارس</option>
                                                <option value="4">أبريل</option>
                                                <option value="5">مايو</option>
                                                <option value="6">يونيو</option>
                                                <option value="7">يوليو</option>
                                                <option value="8">أغسطس</option>
                                                <option value="9">سبتمبر</option>
                                                <option value="10">أكتوبر</option>
                                                <option value="11">نوفمبر</option>
                                                <option value="12">ديسمبر</option>
                                            </select>
                                            <select class="form-select form-select-sm" id="calendarYear" style="width: auto;">
                                                <option value="2024">2024</option>
                                                <option value="2023">2023</option>
                                                <option value="2025">2025</option>
                                            </select>
                                            <button class="btn btn-info btn-sm" onclick="leavesManager.generateCalendar()">
                                                <i class="fas fa-calendar-alt me-1"></i>عرض التقويم
                                            </button>
                                        </div>
                                    </div>
                                    <div id="leaveCalendar" class="calendar-container">
                                        <div class="text-center text-muted p-4">
                                            <i class="fas fa-calendar-alt fa-3x mb-3"></i>
                                            <p>اختر الشهر والسنة لعرض تقويم الإجازات</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Leave Request Modal -->
    <div class="modal fade" id="leaveModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-success text-white">
                    <h5 class="modal-title">
                        <i class="fas fa-calendar-plus me-2"></i>
                        <span id="leaveModalTitle">طلب إجازة جديدة</span>
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="leaveForm">
                        <input type="hidden" id="leaveId">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="leaveEmployee" class="form-label">الموظف *</label>
                                <select class="form-select" id="leaveEmployee" required>
                                    <option value="">اختر الموظف</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="leaveType" class="form-label">نوع الإجازة *</label>
                                <select class="form-select" id="leaveType" required>
                                    <option value="">اختر نوع الإجازة</option>
                                    <option value="annual">إجازة سنوية</option>
                                    <option value="sick">إجازة مرضية</option>
                                    <option value="emergency">إجازة طارئة</option>
                                    <option value="maternity">إجازة أمومة</option>
                                    <option value="paternity">إجازة أبوة</option>
                                    <option value="unpaid">إجازة بدون راتب</option>
                                    <option value="study">إجازة دراسية</option>
                                    <option value="hajj">إجازة حج</option>
                                </select>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="leaveStartDate" class="form-label">تاريخ البداية *</label>
                                <input type="date" class="form-control" id="leaveStartDate" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="leaveEndDate" class="form-label">تاريخ النهاية *</label>
                                <input type="date" class="form-control" id="leaveEndDate" required>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="leaveDays" class="form-label">عدد الأيام</label>
                                <input type="number" class="form-control" id="leaveDays" readonly>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="leaveStatus" class="form-label">الحالة *</label>
                                <select class="form-select" id="leaveStatus" required>
                                    <option value="pending">في الانتظار</option>
                                    <option value="approved">معتمدة</option>
                                    <option value="rejected">مرفوضة</option>
                                    <option value="cancelled">ملغية</option>
                                </select>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="leaveReason" class="form-label">سبب الإجازة *</label>
                            <textarea class="form-control" id="leaveReason" rows="3" required placeholder="أدخل سبب طلب الإجازة..."></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="leaveNotes" class="form-label">ملاحظات إضافية</label>
                            <textarea class="form-control" id="leaveNotes" rows="2" placeholder="أدخل أي ملاحظات إضافية..."></textarea>
                        </div>
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="leaveWithPay">
                                <label class="form-check-label" for="leaveWithPay">
                                    إجازة براتب
                                </label>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-success" id="saveLeaveBtn">
                        <i class="fas fa-save me-1"></i>حفظ الطلب
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Leave Balance Modal -->
    <div class="modal fade" id="balanceModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title">
                        <i class="fas fa-balance-scale me-2"></i>
                        تعديل رصيد الإجازات
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="balanceForm">
                        <input type="hidden" id="balanceEmployeeId">
                        <div class="mb-3">
                            <label for="balanceEmployeeName" class="form-label">الموظف</label>
                            <input type="text" class="form-control" id="balanceEmployeeName" readonly>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="annualLeaveBalance" class="form-label">رصيد الإجازة السنوية</label>
                                <input type="number" class="form-control" id="annualLeaveBalance" min="0" max="365">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="sickLeaveBalance" class="form-label">رصيد الإجازة المرضية</label>
                                <input type="number" class="form-control" id="sickLeaveBalance" min="0" max="365">
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" id="saveBalanceBtn">
                        <i class="fas fa-save me-1"></i>حفظ التغييرات
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteLeaveModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title">تأكيد الحذف</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>هل أنت متأكد من حذف طلب الإجازة هذا؟</p>
                    <p class="text-muted">لا يمكن التراجع عن هذا الإجراء.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-danger" id="confirmDeleteLeaveBtn">
                        <i class="fas fa-trash me-1"></i>حذف
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="database.js"></script>
    <script src="auth.js"></script>
    <script src="app.js"></script>
    <script src="leaves.js"></script>
</body>
</html>
