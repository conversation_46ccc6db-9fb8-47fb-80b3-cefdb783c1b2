// Document Management System
class DocumentManager {
    constructor() {
        this.documents = [];
        this.filteredDocuments = [];
        this.currentCategory = 'all';
        this.searchTerm = '';
    }

    async init() {
        await this.loadDocuments();
        this.setupEventListeners();
        this.renderDocuments();
    }

    async loadDocuments() {
        try {
            // Load documents from localStorage or IndexedDB
            const storedDocs = localStorage.getItem('employeeDocuments');
            this.documents = storedDocs ? JSON.parse(storedDocs) : [];
            this.filteredDocuments = [...this.documents];
        } catch (error) {
            console.error('Error loading documents:', error);
            this.documents = [];
            this.filteredDocuments = [];
        }
    }

    setupEventListeners() {
        // Category filters
        document.querySelectorAll('.category-filter').forEach(button => {
            button.addEventListener('click', (e) => {
                this.setActiveCategory(e.target.dataset.category);
            });
        });

        // Search functionality
        const searchInput = document.getElementById('documentSearchInput');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.searchTerm = e.target.value.toLowerCase();
                this.applyFilters();
            });
        }

        // File upload
        const fileInput = document.getElementById('documentFileInput');
        const uploadArea = document.getElementById('documentUploadArea');

        if (fileInput && uploadArea) {
            fileInput.addEventListener('change', (e) => {
                this.handleFileUpload(e.target.files);
            });

            // Drag and drop functionality
            uploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                uploadArea.classList.add('dragover');
            });

            uploadArea.addEventListener('dragleave', () => {
                uploadArea.classList.remove('dragover');
            });

            uploadArea.addEventListener('drop', (e) => {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
                this.handleFileUpload(e.dataTransfer.files);
            });
        }

        // Save documents button
        const saveBtn = document.getElementById('saveDocumentsBtn');
        if (saveBtn) {
            saveBtn.addEventListener('click', () => {
                this.saveDocuments();
            });
        }
    }

    setActiveCategory(category) {
        // Update active button
        document.querySelectorAll('.category-filter').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-category="${category}"]`).classList.add('active');

        this.currentCategory = category;
        this.applyFilters();
    }

    applyFilters() {
        this.filteredDocuments = this.documents.filter(doc => {
            const matchesCategory = this.currentCategory === 'all' || doc.category === this.currentCategory;
            const matchesSearch = !this.searchTerm || 
                doc.name.toLowerCase().includes(this.searchTerm) ||
                doc.description.toLowerCase().includes(this.searchTerm);
            
            return matchesCategory && matchesSearch;
        });

        this.renderDocuments();
    }

    async handleFileUpload(files) {
        const fileArray = Array.from(files);
        
        for (const file of fileArray) {
            if (file.size > 10 * 1024 * 1024) { // 10MB limit
                this.showAlert(`الملف ${file.name} كبير جداً (الحد الأقصى 10 ميجابايت)`, 'warning');
                continue;
            }

            const document = {
                id: Date.now() + Math.random(),
                name: file.name,
                type: this.getFileType(file.name),
                size: file.size,
                category: this.currentCategory === 'all' ? 'personal' : this.currentCategory,
                uploadDate: new Date().toISOString(),
                description: '',
                file: await this.fileToBase64(file)
            };

            this.documents.push(document);
        }

        this.applyFilters();
        this.showAlert(`تم رفع ${fileArray.length} ملف بنجاح`, 'success');
    }

    fileToBase64(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.readAsDataURL(file);
            reader.onload = () => resolve(reader.result);
            reader.onerror = error => reject(error);
        });
    }

    getFileType(filename) {
        const extension = filename.split('.').pop().toLowerCase();
        
        switch (extension) {
            case 'pdf':
                return 'pdf';
            case 'doc':
            case 'docx':
                return 'word';
            case 'xls':
            case 'xlsx':
                return 'excel';
            case 'jpg':
            case 'jpeg':
            case 'png':
            case 'gif':
                return 'image';
            default:
                return 'default';
        }
    }

    getFileIcon(type) {
        switch (type) {
            case 'pdf':
                return 'fas fa-file-pdf';
            case 'word':
                return 'fas fa-file-word';
            case 'excel':
                return 'fas fa-file-excel';
            case 'image':
                return 'fas fa-file-image';
            default:
                return 'fas fa-file';
        }
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 بايت';
        
        const k = 1024;
        const sizes = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    renderDocuments() {
        const container = document.getElementById('documentList');
        if (!container) return;

        if (this.filteredDocuments.length === 0) {
            container.innerHTML = `
                <div class="text-center text-muted p-4">
                    <i class="fas fa-folder-open fa-3x mb-3"></i>
                    <p>لا توجد مستندات في هذه الفئة</p>
                </div>
            `;
            return;
        }

        container.innerHTML = this.filteredDocuments.map(doc => `
            <div class="document-item" data-id="${doc.id}">
                <div class="document-icon ${doc.type}">
                    <i class="${this.getFileIcon(doc.type)}"></i>
                </div>
                <div class="document-info">
                    <h6>${doc.name}</h6>
                    <div class="document-meta">
                        <small class="text-muted">
                            ${this.formatFileSize(doc.size)} • 
                            ${new Date(doc.uploadDate).toLocaleDateString('ar-SA')}
                        </small>
                    </div>
                    <div class="document-description">
                        <input type="text" class="form-control form-control-sm" 
                               placeholder="وصف المستند..." 
                               value="${doc.description}"
                               onchange="documentManager.updateDescription('${doc.id}', this.value)">
                    </div>
                </div>
                <div class="document-actions">
                    <button class="btn btn-outline-primary btn-sm" 
                            onclick="documentManager.previewDocument('${doc.id}')" 
                            title="معاينة">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-outline-success btn-sm" 
                            onclick="documentManager.downloadDocument('${doc.id}')" 
                            title="تحميل">
                        <i class="fas fa-download"></i>
                    </button>
                    <button class="btn btn-outline-danger btn-sm" 
                            onclick="documentManager.deleteDocument('${doc.id}')" 
                            title="حذف">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `).join('');
    }

    updateDescription(docId, description) {
        const doc = this.documents.find(d => d.id == docId);
        if (doc) {
            doc.description = description;
        }
    }

    previewDocument(docId) {
        const doc = this.documents.find(d => d.id == docId);
        if (!doc) return;

        const modal = new bootstrap.Modal(document.getElementById('documentPreviewModal'));
        const title = document.getElementById('documentPreviewTitle');
        const content = document.getElementById('documentPreviewContent');

        title.textContent = doc.name;

        if (doc.type === 'image') {
            content.innerHTML = `<img src="${doc.file}" alt="${doc.name}" class="img-fluid">`;
        } else if (doc.type === 'pdf') {
            content.innerHTML = `<iframe src="${doc.file}" width="100%" height="500px"></iframe>`;
        } else {
            content.innerHTML = `
                <div class="text-center p-4">
                    <i class="${this.getFileIcon(doc.type)} fa-4x mb-3"></i>
                    <h5>${doc.name}</h5>
                    <p class="text-muted">لا يمكن معاينة هذا النوع من الملفات</p>
                    <button class="btn btn-primary" onclick="documentManager.downloadDocument('${doc.id}')">
                        <i class="fas fa-download me-1"></i>تحميل الملف
                    </button>
                </div>
            `;
        }

        modal.show();
    }

    downloadDocument(docId) {
        const doc = this.documents.find(d => d.id == docId);
        if (!doc) return;

        const link = document.createElement('a');
        link.href = doc.file;
        link.download = doc.name;
        link.click();
    }

    deleteDocument(docId) {
        if (confirm('هل أنت متأكد من حذف هذا المستند؟')) {
            this.documents = this.documents.filter(d => d.id != docId);
            this.applyFilters();
            this.showAlert('تم حذف المستند بنجاح', 'success');
        }
    }

    async saveDocuments() {
        try {
            localStorage.setItem('employeeDocuments', JSON.stringify(this.documents));
            this.showAlert('تم حفظ المستندات بنجاح', 'success');
        } catch (error) {
            console.error('Error saving documents:', error);
            this.showAlert('حدث خطأ في حفظ المستندات', 'danger');
        }
    }

    showAlert(message, type = 'info') {
        // Create and show notification
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(notification);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }
}

// Initialize document manager when modal is shown
document.addEventListener('DOMContentLoaded', () => {
    const documentModal = document.getElementById('documentModal');
    if (documentModal) {
        documentModal.addEventListener('shown.bs.modal', () => {
            if (!window.documentManager) {
                window.documentManager = new DocumentManager();
                window.documentManager.init();
            }
        });
    }
});
