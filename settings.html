<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الإعدادات - نظام إدارة الموظفين</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="styles.css" rel="stylesheet">
</head>
<body>
    <!-- Sidebar -->
    <nav class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <h4 class="text-white mb-0">
                <i class="fas fa-users-cog me-2"></i>
                <span class="sidebar-text">نظام الموظفين</span>
            </h4>
        </div>
        <ul class="sidebar-menu">
            <li>
                <a href="dashboard.html">
                    <i class="fas fa-tachometer-alt"></i>
                    <span class="sidebar-text">لوحة التحكم</span>
                </a>
            </li>
            <li>
                <a href="employees.html">
                    <i class="fas fa-users"></i>
                    <span class="sidebar-text">إدارة الموظفين</span>
                </a>
            </li>
            <li>
                <a href="payroll.html">
                    <i class="fas fa-calculator"></i>
                    <span class="sidebar-text">حساب الرواتب</span>
                </a>
            </li>
            <li>
                <a href="reports.html">
                    <i class="fas fa-chart-bar"></i>
                    <span class="sidebar-text">التقارير</span>
                </a>
            </li>
            <li>
                <a href="attendance.html">
                    <i class="fas fa-clock"></i>
                    <span class="sidebar-text">الحضور والانصراف</span>
                </a>
            </li>
            <li>
                <a href="settings.html" class="active">
                    <i class="fas fa-cog"></i>
                    <span class="sidebar-text">الإعدادات</span>
                </a>
            </li>
            <li>
                <a href="#" class="logout-btn">
                    <i class="fas fa-sign-out-alt"></i>
                    <span class="sidebar-text">تسجيل الخروج</span>
                </a>
            </li>
        </ul>
    </nav>

    <!-- Main Content -->
    <div class="main-content" id="mainContent">
        <!-- Header -->
        <header class="header">
            <div class="d-flex align-items-center">
                <button class="btn btn-link text-dark me-3" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h3 class="header-title">الإعدادات</h3>
            </div>
            <div class="user-info">
                <span class="user-name me-2"></span>
                <span class="user-role badge bg-primary me-2"></span>
                <div class="user-avatar"></div>
            </div>
        </header>

        <!-- Content Area -->
        <div class="content-area">
            <!-- Settings Tabs -->
            <div class="form-container mb-4">
                <ul class="nav nav-tabs" id="settingsTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="company-tab" data-bs-toggle="tab" data-bs-target="#company" type="button" role="tab">
                            <i class="fas fa-building me-1"></i>
                            معلومات الشركة
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="payroll-tab" data-bs-toggle="tab" data-bs-target="#payroll" type="button" role="tab">
                            <i class="fas fa-calculator me-1"></i>
                            إعدادات الرواتب
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="departments-tab" data-bs-toggle="tab" data-bs-target="#departments" type="button" role="tab">
                            <i class="fas fa-sitemap me-1"></i>
                            الأقسام والوظائف
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="users-tab" data-bs-toggle="tab" data-bs-target="#users" type="button" role="tab">
                            <i class="fas fa-users-cog me-1"></i>
                            إدارة المستخدمين
                        </button>
                    </li>
                </ul>

                <div class="tab-content mt-4" id="settingsTabContent">
                    <!-- Company Settings -->
                    <div class="tab-pane fade show active" id="company" role="tabpanel">
                        <form id="companyForm">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="companyName" class="form-label">اسم الشركة</label>
                                    <input type="text" class="form-control" id="companyName" value="شركة النموذج المحدودة">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="companyPhone" class="form-label">هاتف الشركة</label>
                                    <input type="tel" class="form-control" id="companyPhone">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="companyEmail" class="form-label">البريد الإلكتروني</label>
                                    <input type="email" class="form-control" id="companyEmail">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="companyWebsite" class="form-label">الموقع الإلكتروني</label>
                                    <input type="url" class="form-control" id="companyWebsite">
                                </div>
                                <div class="col-12 mb-3">
                                    <label for="companyAddress" class="form-label">عنوان الشركة</label>
                                    <textarea class="form-control" id="companyAddress" rows="3">الرياض، المملكة العربية السعودية</textarea>
                                </div>
                            </div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>
                                حفظ معلومات الشركة
                            </button>
                        </form>
                    </div>

                    <!-- Payroll Settings -->
                    <div class="tab-pane fade" id="payroll" role="tabpanel">
                        <form id="payrollForm">
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="workingDaysPerMonth" class="form-label">أيام العمل في الشهر</label>
                                    <input type="number" class="form-control" id="workingDaysPerMonth" value="22" min="1" max="31">
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="workingHoursPerDay" class="form-label">ساعات العمل في اليوم</label>
                                    <input type="number" class="form-control" id="workingHoursPerDay" value="8" min="1" max="24">
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="overtimeRate" class="form-label">معدل الساعات الإضافية</label>
                                    <input type="number" class="form-control" id="overtimeRate" value="1.5" step="0.1" min="1">
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="socialInsuranceRate" class="form-label">نسبة التأمين الاجتماعي (%)</label>
                                    <input type="number" class="form-control" id="socialInsuranceRate" value="9" step="0.1" min="0" max="100">
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="currency" class="form-label">العملة</label>
                                    <select class="form-select" id="currency">
                                        <option value="omr">ريال عماني</option>
                                        <option value="USD">دولار أمريكي</option>
                                        <option value="EUR">يورو</option>
                                    </select>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="payrollDay" class="form-label">يوم صرف الراتب</label>
                                    <input type="number" class="form-control" id="payrollDay" value="25" min="1" max="31">
                                </div>
                            </div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>
                                حفظ إعدادات الرواتب
                            </button>
                        </form>
                    </div>

                    <!-- Departments and Positions -->
                    <div class="tab-pane fade" id="departments" role="tabpanel">
                        <div class="row">
                            <div class="col-lg-6 mb-4">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h6>الأقسام</h6>
                                    <button class="btn btn-sm btn-success" id="addDepartmentBtn">
                                        <i class="fas fa-plus me-1"></i>
                                        إضافة قسم
                                    </button>
                                </div>
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>اسم القسم</th>
                                                <th>الوصف</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody id="departmentsTable">
                                            <!-- Departments will be loaded here -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <div class="col-lg-6 mb-4">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h6>الوظائف</h6>
                                    <button class="btn btn-sm btn-success" id="addPositionBtn">
                                        <i class="fas fa-plus me-1"></i>
                                        إضافة وظيفة
                                    </button>
                                </div>
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>المسمى الوظيفي</th>
                                                <th>القسم</th>
                                                <th>الراتب الأساسي</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody id="positionsTable">
                                            <!-- Positions will be loaded here -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Users Management -->
                    <div class="tab-pane fade" id="users" role="tabpanel">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6>المستخدمين</h6>
                            <button class="btn btn-success" id="addUserBtn">
                                <i class="fas fa-user-plus me-1"></i>
                                إضافة مستخدم
                            </button>
                        </div>
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>اسم المستخدم</th>
                                        <th>الاسم</th>
                                        <th>الدور</th>
                                        <th>الحالة</th>
                                        <th>آخر دخول</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="usersTable">
                                    <!-- Users will be loaded here -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- System Information -->
            <div class="form-container">
                <h5 class="mb-3">معلومات النظام</h5>
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>إصدار النظام:</strong> 1.0.0</p>
                        <p><strong>تاريخ آخر تحديث:</strong> <span id="lastUpdate">اليوم</span></p>
                        <p><strong>قاعدة البيانات:</strong> IndexedDB (محلية)</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>عدد الموظفين:</strong> <span id="totalEmployeesInfo">0</span></p>
                        <p><strong>عدد المستخدمين:</strong> <span id="totalUsersInfo">0</span></p>
                        <p><strong>حجم البيانات:</strong> <span id="dataSize">غير محدد</span></p>
                    </div>
                </div>
                <div class="mt-3">
                    <button class="btn btn-warning me-2" id="backupBtn">
                        <i class="fas fa-download me-1"></i>
                        نسخ احتياطي للبيانات
                    </button>
                    <button class="btn btn-info me-2" id="restoreBtn">
                        <i class="fas fa-upload me-1"></i>
                        استعادة البيانات
                    </button>
                    <button class="btn btn-danger" id="resetBtn">
                        <i class="fas fa-trash me-1"></i>
                        إعادة تعيين النظام
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="database.js"></script>
    <script src="auth.js"></script>
    <script>
        // Settings Management
        class SettingsManager {
            constructor() {
                this.settings = {};
                this.departments = [];
                this.positions = [];
                this.users = [];
            }

            async init() {
                await this.loadData();
                this.setupEventListeners();
                this.populateSettings();
                this.setupSidebar();
                this.updateSystemInfo();
            }

            async loadData() {
                try {
                    const settingomrray = await dbManager.getAll('settings');
                    this.settings = {};
                    settingomrray.forEach(setting => {
                        this.settings[setting.key] = setting.value;
                    });

                    this.departments = await dbManager.getAll('departments');
                    this.positions = await dbManager.getAll('positions');
                    this.users = await dbManager.getAll('users');
                } catch (error) {
                    console.error('Error loading settings:', error);
                }
            }

            populateSettings() {
                // Company settings
                document.getElementById('companyName').value = this.settings.companyName || '';
                document.getElementById('companyAddress').value = this.settings.companyAddress || '';

                // Payroll settings
                document.getElementById('workingDaysPerMonth').value = this.settings.workingDaysPerMonth || 22;
                document.getElementById('workingHoursPerDay').value = this.settings.workingHoursPerDay || 8;
                document.getElementById('overtimeRate').value = this.settings.overtimeRate || 1.5;
                document.getElementById('socialInsuranceRate').value = this.settings.socialInsuranceRate || 9;
                document.getElementById('currency').value = this.settings.currency || 'omr';

                // Load departments and positions
                this.loadDepartmentsTable();
                this.loadPositionsTable();
                this.loadUsersTable();
            }

            loadDepartmentsTable() {
                const tbody = document.getElementById('departmentsTable');
                tbody.innerHTML = this.departments.map(dept => `
                    <tr>
                        <td>${dept.name}</td>
                        <td>${dept.description || '-'}</td>
                        <td>
                            <button class="btn btn-sm btn-outline-danger" onclick="settingsManager.deleteDepartment(${dept.id})">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                `).join('');
            }

            loadPositionsTable() {
                const tbody = document.getElementById('positionsTable');
                tbody.innerHTML = this.positions.map(pos => `
                    <tr>
                        <td>${pos.title}</td>
                        <td>${pos.department}</td>
                        <td>${pos.baseSalary || '-'}</td>
                        <td>
                            <button class="btn btn-sm btn-outline-danger" onclick="settingsManager.deletePosition(${pos.id})">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                `).join('');
            }

            loadUsersTable() {
                const tbody = document.getElementById('usersTable');
                tbody.innerHTML = this.users.map(user => `
                    <tr>
                        <td>${user.username}</td>
                        <td>${user.name}</td>
                        <td>${this.getRoleText(user.role)}</td>
                        <td>
                            <span class="badge ${user.active ? 'bg-success' : 'bg-danger'}">
                                ${user.active ? 'نشط' : 'غير نشط'}
                            </span>
                        </td>
                        <td>-</td>
                        <td>
                            <button class="btn btn-sm btn-outline-warning me-1" onclick="settingsManager.toggleUserStatus(${user.id})">
                                <i class="fas fa-power-off"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-danger" onclick="settingsManager.deleteUser(${user.id})">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                `).join('');
            }

            setupEventListeners() {
                // Company form
                document.getElementById('companyForm').addEventListener('submit', (e) => this.saveCompanySettings(e));

                // Payroll form
                document.getElementById('payrollForm').addEventListener('submit', (e) => this.savePayrollSettings(e));

                // Add buttons
                document.getElementById('addDepartmentBtn').addEventListener('click', () => this.addDepartment());
                document.getElementById('addPositionBtn').addEventListener('click', () => this.addPosition());
                document.getElementById('addUserBtn').addEventListener('click', () => this.addUser());

                // System buttons
                document.getElementById('backupBtn').addEventListener('click', () => this.backupData());
                document.getElementById('restoreBtn').addEventListener('click', () => this.restoreData());
                document.getElementById('resetBtn').addEventListener('click', () => this.resetSystem());
            }

            setupSidebar() {
                const sidebarToggle = document.getElementById('sidebarToggle');
                const sidebar = document.getElementById('sidebar');
                const mainContent = document.getElementById('mainContent');

                if (sidebarToggle) {
                    sidebarToggle.addEventListener('click', () => {
                        sidebar.classList.toggle('collapsed');
                        mainContent.classList.toggle('expanded');
                    });
                }
            }

            async saveCompanySettings(e) {
                e.preventDefault();
                
                const settings = {
                    companyName: document.getElementById('companyName').value,
                    companyPhone: document.getElementById('companyPhone').value,
                    companyEmail: document.getElementById('companyEmail').value,
                    companyWebsite: document.getElementById('companyWebsite').value,
                    companyAddress: document.getElementById('companyAddress').value
                };

                try {
                    for (const [key, value] of Object.entries(settings)) {
                        await dbManager.update('settings', { key, value });
                    }
                    alert('تم حفظ معلومات الشركة بنجاح');
                } catch (error) {
                    console.error('Error saving company settings:', error);
                    alert('حدث خطأ في حفظ الإعدادات');
                }
            }

            async savePayrollSettings(e) {
                e.preventDefault();
                
                const settings = {
                    workingDaysPerMonth: parseInt(document.getElementById('workingDaysPerMonth').value),
                    workingHoursPerDay: parseInt(document.getElementById('workingHoursPerDay').value),
                    overtimeRate: parseFloat(document.getElementById('overtimeRate').value),
                    socialInsuranceRate: parseFloat(document.getElementById('socialInsuranceRate').value),
                    currency: document.getElementById('currency').value,
                    payrollDay: parseInt(document.getElementById('payrollDay').value)
                };

                try {
                    for (const [key, value] of Object.entries(settings)) {
                        await dbManager.update('settings', { key, value });
                    }
                    alert('تم حفظ إعدادات الرواتب بنجاح');
                } catch (error) {
                    console.error('Error saving payroll settings:', error);
                    alert('حدث خطأ في حفظ الإعدادات');
                }
            }

            addDepartment() {
                const name = prompt('اسم القسم الجديد:');
                if (name) {
                    const description = prompt('وصف القسم (اختياري):');
                    this.saveDepartment({ name, description });
                }
            }

            async saveDepartment(dept) {
                try {
                    await dbManager.add('departments', dept);
                    this.departments.push(dept);
                    this.loadDepartmentsTable();
                    alert('تم إضافة القسم بنجاح');
                } catch (error) {
                    console.error('Error saving department:', error);
                    alert('حدث خطأ في إضافة القسم');
                }
            }

            updateSystemInfo() {
                document.getElementById('lastUpdate').textContent = new Date().toLocaleDateString('ar-SA');
                document.getElementById('totalEmployeesInfo').textContent = '0'; // Would be loaded from database
                document.getElementById('totalUsersInfo').textContent = this.users.length;
            }

            getRoleText(role) {
                switch (role) {
                    case 'admin': return 'مدير النظام';
                    case 'hr': return 'موارد بشرية';
                    case 'accountant': return 'محاسب';
                    default: return role;
                }
            }

            backupData() {
                alert('ميزة النسخ الاحتياطي قيد التطوير');
            }

            restoreData() {
                alert('ميزة استعادة البيانات قيد التطوير');
            }

            resetSystem() {
                if (confirm('هل أنت متأكد من إعادة تعيين النظام؟ سيتم حذف جميع البيانات!')) {
                    alert('ميزة إعادة التعيين قيد التطوير');
                }
            }
        }

        // Initialize settings manager
        const settingsManager = new SettingsManager();

        document.addEventListener('DOMContentLoaded', async function() {
            await initializeDatabase();
            await settingsManager.init();
        });
    </script>
</body>
</html>
